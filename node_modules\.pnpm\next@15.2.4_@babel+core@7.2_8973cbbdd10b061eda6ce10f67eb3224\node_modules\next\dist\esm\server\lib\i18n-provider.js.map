{"version": 3, "sources": ["../../../src/server/lib/i18n-provider.ts"], "sourcesContent": ["import type { DomainLocale, I18NConfig } from '../config-shared'\nimport { getRequestMeta, type NextIncomingMessage } from '../request-meta'\n\n/**\n * The result of matching a locale aware route.\n */\nexport interface LocaleAnalysisResult {\n  /**\n   * The pathname without the locale prefix (if any).\n   */\n  pathname: string\n\n  /**\n   * The detected locale. If no locale was detected, this will be `undefined`.\n   */\n  detectedLocale?: string\n\n  /**\n   * True if the locale was inferred from the default locale.\n   */\n  inferredFromDefault: boolean\n}\n\ntype LocaleAnalysisOptions = {\n  /**\n   * When provided, it will be used as the default locale if the locale\n   * cannot be inferred from the pathname.\n   */\n  defaultLocale?: string\n}\n\n/**\n * The I18NProvider is used to match locale aware routes, detect the locale from\n * the pathname and hostname and normalize the pathname by removing the locale\n * prefix.\n */\nexport class I18NProvider {\n  private readonly lowerCaseLocales: ReadonlyArray<string>\n  private readonly lowerCaseDomains?: ReadonlyArray<\n    DomainLocale & {\n      // The configuration references a domain with an optional port, but the\n      // hostname is always the domain without the port and is used for\n      // matching.\n      hostname: string\n    }\n  >\n\n  constructor(public readonly config: Readonly<I18NConfig>) {\n    if (!config.locales.length) {\n      throw new Error('Invariant: No locales provided')\n    }\n\n    this.lowerCaseLocales = config.locales.map((locale) => locale.toLowerCase())\n    this.lowerCaseDomains = config.domains?.map((domainLocale) => {\n      const domain = domainLocale.domain.toLowerCase()\n      return {\n        defaultLocale: domainLocale.defaultLocale.toLowerCase(),\n        hostname: domain.split(':', 1)[0],\n        domain,\n        locales: domainLocale.locales?.map((locale) => locale.toLowerCase()),\n        http: domainLocale.http,\n      }\n    })\n  }\n\n  /**\n   * Detects the domain locale from the hostname and the detected locale if\n   * provided.\n   *\n   * @param hostname The hostname to detect the domain locale from, this must be lowercased.\n   * @param detectedLocale The detected locale to use if the hostname does not match.\n   * @returns The domain locale if found, `undefined` otherwise.\n   */\n  public detectDomainLocale(\n    hostname?: string,\n    detectedLocale?: string\n  ): DomainLocale | undefined {\n    if (!hostname || !this.lowerCaseDomains || !this.config.domains) return\n\n    if (detectedLocale) detectedLocale = detectedLocale.toLowerCase()\n\n    for (let i = 0; i < this.lowerCaseDomains.length; i++) {\n      const domainLocale = this.lowerCaseDomains[i]\n      if (\n        // We assume that the hostname is already lowercased.\n        domainLocale.hostname === hostname ||\n        // Configuration validation ensures that the locale is not repeated in\n        // other domains locales.\n        domainLocale.locales?.some((locale) => locale === detectedLocale)\n      ) {\n        return this.config.domains[i]\n      }\n    }\n\n    return\n  }\n\n  /**\n   * Pulls the pre-computed locale and inference results from the query\n   * object.\n   *\n   * @param req the request object\n   * @param pathname the pathname that could contain a locale prefix\n   * @returns the locale analysis result\n   */\n  public fromRequest(\n    req: NextIncomingMessage,\n    pathname: string\n  ): LocaleAnalysisResult {\n    const detectedLocale = getRequestMeta(req, 'locale')\n\n    // If a locale was detected on the query, analyze the pathname to ensure\n    // that the locale matches.\n    if (detectedLocale) {\n      const analysis = this.analyze(pathname)\n\n      // If the analysis contained a locale we should validate it against the\n      // query and strip it from the pathname.\n      if (analysis.detectedLocale) {\n        if (analysis.detectedLocale !== detectedLocale) {\n          throw new Error(\n            `Invariant: The detected locale does not match the locale in the query. Expected to find '${detectedLocale}' in '${pathname}' but found '${analysis.detectedLocale}'}`\n          )\n        }\n\n        pathname = analysis.pathname\n      }\n    }\n\n    return {\n      pathname,\n      detectedLocale,\n      inferredFromDefault:\n        getRequestMeta(req, 'localeInferredFromDefault') ?? false,\n    }\n  }\n\n  /**\n   * Analyzes the pathname for a locale and returns the pathname without it.\n   *\n   * @param pathname The pathname that could contain a locale prefix.\n   * @param options The options to use when matching the locale.\n   * @returns The matched locale and the pathname without the locale prefix\n   *          (if any).\n   */\n  public analyze(\n    pathname: string,\n    options: LocaleAnalysisOptions = {}\n  ): LocaleAnalysisResult {\n    let detectedLocale: string | undefined = options.defaultLocale\n\n    // By default, we assume that the default locale was inferred if there was\n    // no detected locale.\n    let inferredFromDefault = typeof detectedLocale === 'string'\n\n    // The first segment will be empty, because it has a leading `/`. If\n    // there is no further segment, there is no locale (or it's the default).\n    const segments = pathname.split('/', 2)\n    if (!segments[1])\n      return {\n        detectedLocale,\n        pathname,\n        inferredFromDefault,\n      }\n\n    // The second segment will contain the locale part if any.\n    const segment = segments[1].toLowerCase()\n\n    // See if the segment matches one of the locales. If it doesn't, there is\n    // no locale (or it's the default).\n    const index = this.lowerCaseLocales.indexOf(segment)\n    if (index < 0)\n      return {\n        detectedLocale,\n        pathname,\n        inferredFromDefault,\n      }\n\n    // Return the case-sensitive locale.\n    detectedLocale = this.config.locales[index]\n    inferredFromDefault = false\n\n    // Remove the `/${locale}` part of the pathname.\n    pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n    return {\n      detectedLocale,\n      pathname,\n      inferredFromDefault,\n    }\n  }\n}\n"], "names": ["getRequestMeta", "I18NProvider", "constructor", "config", "locales", "length", "Error", "lowerCaseLocales", "map", "locale", "toLowerCase", "lowerCaseDomains", "domains", "domainLocale", "domain", "defaultLocale", "hostname", "split", "http", "detectDomainLocale", "detectedLocale", "i", "some", "fromRequest", "req", "pathname", "analysis", "analyze", "inferredFromDefault", "options", "segments", "segment", "index", "indexOf", "slice"], "mappings": "AACA,SAASA,cAAc,QAAkC,kBAAiB;AA8B1E;;;;CAIC,GACD,OAAO,MAAMC;IAWXC,YAAY,AAAgBC,MAA4B,CAAE;YAMhCA;aANEA,SAAAA;QAC1B,IAAI,CAACA,OAAOC,OAAO,CAACC,MAAM,EAAE;YAC1B,MAAM,qBAA2C,CAA3C,IAAIC,MAAM,mCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0C;QAClD;QAEA,IAAI,CAACC,gBAAgB,GAAGJ,OAAOC,OAAO,CAACI,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QACzE,IAAI,CAACC,gBAAgB,IAAGR,kBAAAA,OAAOS,OAAO,qBAAdT,gBAAgBK,GAAG,CAAC,CAACK;gBAMhCA;YALX,MAAMC,SAASD,aAAaC,MAAM,CAACJ,WAAW;YAC9C,OAAO;gBACLK,eAAeF,aAAaE,aAAa,CAACL,WAAW;gBACrDM,UAAUF,OAAOG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;gBACjCH;gBACAV,OAAO,GAAES,wBAAAA,aAAaT,OAAO,qBAApBS,sBAAsBL,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;gBACjEQ,MAAML,aAAaK,IAAI;YACzB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,AAAOC,mBACLH,QAAiB,EACjBI,cAAuB,EACG;QAC1B,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACL,gBAAgB,IAAI,CAAC,IAAI,CAACR,MAAM,CAACS,OAAO,EAAE;QAEjE,IAAIQ,gBAAgBA,iBAAiBA,eAAeV,WAAW;QAE/D,IAAK,IAAIW,IAAI,GAAGA,IAAI,IAAI,CAACV,gBAAgB,CAACN,MAAM,EAAEgB,IAAK;gBAKnD,sEAAsE;YACtE,yBAAyB;YACzBR;YANF,MAAMA,eAAe,IAAI,CAACF,gBAAgB,CAACU,EAAE;YAC7C,IACE,qDAAqD;YACrDR,aAAaG,QAAQ,KAAKA,cAG1BH,wBAAAA,aAAaT,OAAO,qBAApBS,sBAAsBS,IAAI,CAAC,CAACb,SAAWA,WAAWW,kBAClD;gBACA,OAAO,IAAI,CAACjB,MAAM,CAACS,OAAO,CAACS,EAAE;YAC/B;QACF;QAEA;IACF;IAEA;;;;;;;GAOC,GACD,AAAOE,YACLC,GAAwB,EACxBC,QAAgB,EACM;QACtB,MAAML,iBAAiBpB,eAAewB,KAAK;QAE3C,wEAAwE;QACxE,2BAA2B;QAC3B,IAAIJ,gBAAgB;YAClB,MAAMM,WAAW,IAAI,CAACC,OAAO,CAACF;YAE9B,uEAAuE;YACvE,wCAAwC;YACxC,IAAIC,SAASN,cAAc,EAAE;gBAC3B,IAAIM,SAASN,cAAc,KAAKA,gBAAgB;oBAC9C,MAAM,qBAEL,CAFK,IAAId,MACR,CAAC,yFAAyF,EAAEc,eAAe,MAAM,EAAEK,SAAS,aAAa,EAAEC,SAASN,cAAc,CAAC,EAAE,CAAC,GADlK,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEAK,WAAWC,SAASD,QAAQ;YAC9B;QACF;QAEA,OAAO;YACLA;YACAL;YACAQ,qBACE5B,eAAewB,KAAK,gCAAgC;QACxD;IACF;IAEA;;;;;;;GAOC,GACD,AAAOG,QACLF,QAAgB,EAChBI,UAAiC,CAAC,CAAC,EACb;QACtB,IAAIT,iBAAqCS,QAAQd,aAAa;QAE9D,0EAA0E;QAC1E,sBAAsB;QACtB,IAAIa,sBAAsB,OAAOR,mBAAmB;QAEpD,oEAAoE;QACpE,yEAAyE;QACzE,MAAMU,WAAWL,SAASR,KAAK,CAAC,KAAK;QACrC,IAAI,CAACa,QAAQ,CAAC,EAAE,EACd,OAAO;YACLV;YACAK;YACAG;QACF;QAEF,0DAA0D;QAC1D,MAAMG,UAAUD,QAAQ,CAAC,EAAE,CAACpB,WAAW;QAEvC,yEAAyE;QACzE,mCAAmC;QACnC,MAAMsB,QAAQ,IAAI,CAACzB,gBAAgB,CAAC0B,OAAO,CAACF;QAC5C,IAAIC,QAAQ,GACV,OAAO;YACLZ;YACAK;YACAG;QACF;QAEF,oCAAoC;QACpCR,iBAAiB,IAAI,CAACjB,MAAM,CAACC,OAAO,CAAC4B,MAAM;QAC3CJ,sBAAsB;QAEtB,gDAAgD;QAChDH,WAAWA,SAASS,KAAK,CAACd,eAAef,MAAM,GAAG,MAAM;QAExD,OAAO;YACLe;YACAK;YACAG;QACF;IACF;AACF"}