{"version": 3, "sources": ["../../src/server/load-default-error-components.ts"], "sourcesContent": ["import type {\n  AppType,\n  DocumentType,\n  NextComponentType,\n} from '../shared/lib/utils'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type {\n  PageConfig,\n  GetStaticPaths,\n  GetServerSideProps,\n  GetStaticProps,\n} from '../types'\nimport type { RouteModule } from './route-modules/route-module'\nimport type { BuildManifest } from './get-page-files'\n\nimport { BUILD_MANIFEST } from '../shared/lib/constants'\nimport { join } from 'path'\nimport { interopDefault } from '../lib/interop-default'\nimport { getTracer } from './lib/trace/tracer'\nimport { LoadComponentsSpan } from './lib/trace/constants'\nimport { loadManifestWithRetries } from './load-components'\nexport type ManifestItem = {\n  id: number | string\n  files: string[]\n}\n\nexport type ReactLoadableManifest = { [moduleId: string]: ManifestItem }\n\nexport type LoadComponentsReturnType = {\n  Component: NextComponentType\n  pageConfig: PageConfig\n  buildManifest: BuildManifest\n  subresourceIntegrityManifest?: Record<string, string>\n  reactLoadableManifest: ReactLoadableManifest\n  clientReferenceManifest?: ClientReferenceManifest\n  serverActionsManifest?: any\n  Document: DocumentType\n  App: AppType\n  getStaticProps?: GetStaticProps\n  getStaticPaths?: GetStaticPaths\n  getServerSideProps?: GetServerSideProps\n  ComponentMod: any\n  routeModule: RouteModule\n  isAppPath?: boolean\n  page: string\n}\n\nasync function loadDefaultErrorComponentsImpl(\n  distDir: string\n): Promise<LoadComponentsReturnType> {\n  const Document = interopDefault(require('next/dist/pages/_document'))\n  const AppMod = require('next/dist/pages/_app')\n  const App = interopDefault(AppMod)\n\n  // Load the compiled route module for this builtin error.\n  // TODO: (wyattjoh) replace this with just exporting the route module when the transition is complete\n  const ComponentMod =\n    require('./route-modules/pages/builtin/_error') as typeof import('./route-modules/pages/builtin/_error')\n  const Component = ComponentMod.routeModule.userland.default\n\n  return {\n    App,\n    Document,\n    Component,\n    pageConfig: {},\n    buildManifest: (await loadManifestWithRetries(\n      join(distDir, `fallback-${BUILD_MANIFEST}`)\n    )) as BuildManifest,\n    reactLoadableManifest: {},\n    ComponentMod,\n    page: '/_error',\n    routeModule: ComponentMod.routeModule,\n  }\n}\nexport const loadDefaultErrorComponents = getTracer().wrap(\n  LoadComponentsSpan.loadDefaultErrorComponents,\n  loadDefaultErrorComponentsImpl\n)\n"], "names": ["BUILD_MANIFEST", "join", "interopDefault", "getTracer", "LoadComponentsSpan", "loadManifestWithRetries", "loadDefaultErrorComponentsImpl", "distDir", "Document", "require", "AppMod", "App", "ComponentMod", "Component", "routeModule", "userland", "default", "pageConfig", "buildManifest", "reactLoadableManifest", "page", "loadDefaultErrorComponents", "wrap"], "mappings": "AAeA,SAASA,cAAc,QAAQ,0BAAyB;AACxD,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,uBAAuB,QAAQ,oBAAmB;AA2B3D,eAAeC,+BACbC,OAAe;IAEf,MAAMC,WAAWN,eAAeO,QAAQ;IACxC,MAAMC,SAASD,QAAQ;IACvB,MAAME,MAAMT,eAAeQ;IAE3B,yDAAyD;IACzD,qGAAqG;IACrG,MAAME,eACJH,QAAQ;IACV,MAAMI,YAAYD,aAAaE,WAAW,CAACC,QAAQ,CAACC,OAAO;IAE3D,OAAO;QACLL;QACAH;QACAK;QACAI,YAAY,CAAC;QACbC,eAAgB,MAAMb,wBACpBJ,KAAKM,SAAS,CAAC,SAAS,EAAEP,gBAAgB;QAE5CmB,uBAAuB,CAAC;QACxBP;QACAQ,MAAM;QACNN,aAAaF,aAAaE,WAAW;IACvC;AACF;AACA,OAAO,MAAMO,6BAA6BlB,YAAYmB,IAAI,CACxDlB,mBAAmBiB,0BAA0B,EAC7Cf,gCACD"}