{"version": 3, "sources": ["../../../src/server/dev/dev-indicator-middleware.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport { middlewareResponse } from '../../client/components/react-dev-overlay/server/middleware-response'\nimport * as Log from '../../build/output/log'\nimport { devIndicatorServerState } from './dev-indicator-server-state'\n\nconst DISABLE_DEV_INDICATOR_PREFIX = '/__nextjs_disable_dev_indicator'\n\nconst COOLDOWN_TIME_MS = process.env.__NEXT_DEV_INDICATOR_COOLDOWN_MS\n  ? parseInt(process.env.__NEXT_DEV_INDICATOR_COOLDOWN_MS)\n  : // 1 day from now\n    1000 * 60 * 60 * 24\n\nexport function getDisableDevIndicatorMiddleware() {\n  return async function disableDevIndicatorMiddleware(\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    try {\n      const { pathname } = new URL(`http://n${req.url}`)\n\n      if (!pathname.startsWith(DISABLE_DEV_INDICATOR_PREFIX)) {\n        return next()\n      }\n\n      if (req.method !== 'POST') {\n        return middlewareResponse.methodNotAllowed(res)\n      }\n\n      devIndicatorServerState.disabledUntil = Date.now() + COOLDOWN_TIME_MS\n\n      return middlewareResponse.noContent(res)\n    } catch (err) {\n      Log.error(\n        'Failed to disable the dev indicator:',\n        err instanceof Error ? err.message : err\n      )\n      return middlewareResponse.internalServerError(res)\n    }\n  }\n}\n"], "names": ["middlewareResponse", "Log", "devIndicatorServerState", "DISABLE_DEV_INDICATOR_PREFIX", "COOLDOWN_TIME_MS", "process", "env", "__NEXT_DEV_INDICATOR_COOLDOWN_MS", "parseInt", "getDisableDevIndicatorMiddleware", "disableDevIndicatorMiddleware", "req", "res", "next", "pathname", "URL", "url", "startsWith", "method", "methodNotAllowed", "disabledUntil", "Date", "now", "noContent", "err", "error", "Error", "message", "internalServerError"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,uEAAsE;AACzG,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,uBAAuB,QAAQ,+BAA8B;AAEtE,MAAMC,+BAA+B;AAErC,MAAMC,mBAAmBC,QAAQC,GAAG,CAACC,gCAAgC,GACjEC,SAASH,QAAQC,GAAG,CAACC,gCAAgC,IAErD,OAAO,KAAK,KAAK;AAErB,OAAO,SAASE;IACd,OAAO,eAAeC,8BACpBC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEJ,IAAIK,GAAG,EAAE;YAEjD,IAAI,CAACF,SAASG,UAAU,CAACd,+BAA+B;gBACtD,OAAOU;YACT;YAEA,IAAIF,IAAIO,MAAM,KAAK,QAAQ;gBACzB,OAAOlB,mBAAmBmB,gBAAgB,CAACP;YAC7C;YAEAV,wBAAwBkB,aAAa,GAAGC,KAAKC,GAAG,KAAKlB;YAErD,OAAOJ,mBAAmBuB,SAAS,CAACX;QACtC,EAAE,OAAOY,KAAK;YACZvB,IAAIwB,KAAK,CACP,wCACAD,eAAeE,QAAQF,IAAIG,OAAO,GAAGH;YAEvC,OAAOxB,mBAAmB4B,mBAAmB,CAAChB;QAChD;IACF;AACF"}