{"version": 3, "sources": ["../../../../src/server/lib/router-utils/proxy-request.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextUrlWithParsedQuery } from '../../request-meta'\n\nimport url from 'url'\nimport { stringifyQuery } from '../../server-route-utils'\nimport { Duplex } from 'stream'\nimport { DetachedPromise } from '../../../lib/detached-promise'\n\nexport async function proxyRequest(\n  req: IncomingMessage,\n  res: ServerResponse | Duplex,\n  parsedUrl: NextUrlWithParsedQuery,\n  upgradeHead?: Buffer,\n  reqBody?: any,\n  proxyTimeout?: number | null\n) {\n  const { query } = parsedUrl\n  delete (parsedUrl as any).query\n  parsedUrl.search = stringifyQuery(req as any, query)\n\n  const target = url.format(parsedUrl)\n  const HttpProxy =\n    require('next/dist/compiled/http-proxy') as typeof import('next/dist/compiled/http-proxy')\n\n  const proxy = new HttpProxy({\n    target,\n    changeOrigin: true,\n    ignorePath: true,\n    ws: true,\n    // we limit proxy requests to 30s by default, in development\n    // we don't time out WebSocket requests to allow proxying\n    proxyTimeout: proxyTimeout === null ? undefined : proxyTimeout || 30_000,\n    headers: {\n      'x-forwarded-host': req.headers.host || '',\n    },\n  })\n\n  let finished = false\n\n  // http-proxy does not properly detect a client disconnect in newer\n  // versions of Node.js. This is caused because it only listens for the\n  // `aborted` event on the our request object, but it also fully reads\n  // and closes the request object. Node **will not** fire `aborted` when\n  // the request is already closed. Listening for `close` on our response\n  // object will detect the disconnect, and we can abort the proxy's\n  // connection.\n  proxy.on('proxyReq', (proxyReq) => {\n    res.on('close', () => proxyReq.destroy())\n  })\n\n  proxy.on('proxyRes', (proxyRes) => {\n    if (res.destroyed) {\n      proxyRes.destroy()\n    } else {\n      res.on('close', () => proxyRes.destroy())\n    }\n  })\n\n  proxy.on('proxyRes', (proxyRes, innerReq, innerRes) => {\n    const cleanup = (err: any) => {\n      // cleanup event listeners to allow clean garbage collection\n      proxyRes.removeListener('error', cleanup)\n      proxyRes.removeListener('close', cleanup)\n      innerRes.removeListener('error', cleanup)\n      innerRes.removeListener('close', cleanup)\n\n      // destroy all source streams to propagate the caught event backward\n      innerReq.destroy(err)\n      proxyRes.destroy(err)\n    }\n\n    proxyRes.once('error', cleanup)\n    proxyRes.once('close', cleanup)\n    innerRes.once('error', cleanup)\n    innerRes.once('close', cleanup)\n  })\n\n  const detached = new DetachedPromise<boolean>()\n\n  proxy.on('error', (err) => {\n    console.error(`Failed to proxy ${target}`, err)\n    if (!finished) {\n      finished = true\n      detached.reject(err)\n\n      if (!res.destroyed) {\n        if (!(res instanceof Duplex)) {\n          res.statusCode = 500\n        }\n\n        res.end('Internal Server Error')\n      }\n    }\n  })\n\n  // If upgrade head is present or the response is a Duplex stream, treat as\n  // WebSocket request.\n  if (upgradeHead || res instanceof Duplex) {\n    proxy.on('proxyReqWs', (proxyReq) => {\n      proxyReq.on('close', () => {\n        if (!finished) {\n          finished = true\n          detached.resolve(true)\n        }\n      })\n    })\n    proxy.ws(req, res, upgradeHead)\n    detached.resolve(true)\n  } else {\n    proxy.on('proxyReq', (proxyReq) => {\n      proxyReq.on('close', () => {\n        if (!finished) {\n          finished = true\n          detached.resolve(true)\n        }\n      })\n    })\n    proxy.web(req, res, {\n      buffer: reqBody,\n    })\n  }\n\n  // When the proxy finishes proxying the request, shut down the proxy.\n  return detached.promise.finally(() => {\n    proxy.close()\n  })\n}\n"], "names": ["url", "stringifyQuery", "Duplex", "Detached<PERSON>romise", "proxyRequest", "req", "res", "parsedUrl", "upgradeHead", "reqBody", "proxyTimeout", "query", "search", "target", "format", "HttpProxy", "require", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "ignore<PERSON><PERSON>", "ws", "undefined", "headers", "host", "finished", "on", "proxyReq", "destroy", "proxyRes", "destroyed", "innerReq", "innerRes", "cleanup", "err", "removeListener", "once", "detached", "console", "error", "reject", "statusCode", "end", "resolve", "web", "buffer", "promise", "finally", "close"], "mappings": "AAGA,OAAOA,SAAS,MAAK;AACrB,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SAASC,MAAM,QAAQ,SAAQ;AAC/B,SAASC,eAAe,QAAQ,gCAA+B;AAE/D,OAAO,eAAeC,aACpBC,GAAoB,EACpBC,GAA4B,EAC5BC,SAAiC,EACjCC,WAAoB,EACpBC,OAAa,EACbC,YAA4B;IAE5B,MAAM,EAAEC,KAAK,EAAE,GAAGJ;IAClB,OAAO,AAACA,UAAkBI,KAAK;IAC/BJ,UAAUK,MAAM,GAAGX,eAAeI,KAAYM;IAE9C,MAAME,SAASb,IAAIc,MAAM,CAACP;IAC1B,MAAMQ,YACJC,QAAQ;IAEV,MAAMC,QAAQ,IAAIF,UAAU;QAC1BF;QACAK,cAAc;QACdC,YAAY;QACZC,IAAI;QACJ,4DAA4D;QAC5D,yDAAyD;QACzDV,cAAcA,iBAAiB,OAAOW,YAAYX,gBAAgB;QAClEY,SAAS;YACP,oBAAoBjB,IAAIiB,OAAO,CAACC,IAAI,IAAI;QAC1C;IACF;IAEA,IAAIC,WAAW;IAEf,mEAAmE;IACnE,sEAAsE;IACtE,qEAAqE;IACrE,uEAAuE;IACvE,uEAAuE;IACvE,kEAAkE;IAClE,cAAc;IACdP,MAAMQ,EAAE,CAAC,YAAY,CAACC;QACpBpB,IAAImB,EAAE,CAAC,SAAS,IAAMC,SAASC,OAAO;IACxC;IAEAV,MAAMQ,EAAE,CAAC,YAAY,CAACG;QACpB,IAAItB,IAAIuB,SAAS,EAAE;YACjBD,SAASD,OAAO;QAClB,OAAO;YACLrB,IAAImB,EAAE,CAAC,SAAS,IAAMG,SAASD,OAAO;QACxC;IACF;IAEAV,MAAMQ,EAAE,CAAC,YAAY,CAACG,UAAUE,UAAUC;QACxC,MAAMC,UAAU,CAACC;YACf,4DAA4D;YAC5DL,SAASM,cAAc,CAAC,SAASF;YACjCJ,SAASM,cAAc,CAAC,SAASF;YACjCD,SAASG,cAAc,CAAC,SAASF;YACjCD,SAASG,cAAc,CAAC,SAASF;YAEjC,oEAAoE;YACpEF,SAASH,OAAO,CAACM;YACjBL,SAASD,OAAO,CAACM;QACnB;QAEAL,SAASO,IAAI,CAAC,SAASH;QACvBJ,SAASO,IAAI,CAAC,SAASH;QACvBD,SAASI,IAAI,CAAC,SAASH;QACvBD,SAASI,IAAI,CAAC,SAASH;IACzB;IAEA,MAAMI,WAAW,IAAIjC;IAErBc,MAAMQ,EAAE,CAAC,SAAS,CAACQ;QACjBI,QAAQC,KAAK,CAAC,CAAC,gBAAgB,EAAEzB,QAAQ,EAAEoB;QAC3C,IAAI,CAACT,UAAU;YACbA,WAAW;YACXY,SAASG,MAAM,CAACN;YAEhB,IAAI,CAAC3B,IAAIuB,SAAS,EAAE;gBAClB,IAAI,CAAEvB,CAAAA,eAAeJ,MAAK,GAAI;oBAC5BI,IAAIkC,UAAU,GAAG;gBACnB;gBAEAlC,IAAImC,GAAG,CAAC;YACV;QACF;IACF;IAEA,0EAA0E;IAC1E,qBAAqB;IACrB,IAAIjC,eAAeF,eAAeJ,QAAQ;QACxCe,MAAMQ,EAAE,CAAC,cAAc,CAACC;YACtBA,SAASD,EAAE,CAAC,SAAS;gBACnB,IAAI,CAACD,UAAU;oBACbA,WAAW;oBACXY,SAASM,OAAO,CAAC;gBACnB;YACF;QACF;QACAzB,MAAMG,EAAE,CAACf,KAAKC,KAAKE;QACnB4B,SAASM,OAAO,CAAC;IACnB,OAAO;QACLzB,MAAMQ,EAAE,CAAC,YAAY,CAACC;YACpBA,SAASD,EAAE,CAAC,SAAS;gBACnB,IAAI,CAACD,UAAU;oBACbA,WAAW;oBACXY,SAASM,OAAO,CAAC;gBACnB;YACF;QACF;QACAzB,MAAM0B,GAAG,CAACtC,KAAKC,KAAK;YAClBsC,QAAQnC;QACV;IACF;IAEA,qEAAqE;IACrE,OAAO2B,SAASS,OAAO,CAACC,OAAO,CAAC;QAC9B7B,MAAM8B,KAAK;IACb;AACF"}