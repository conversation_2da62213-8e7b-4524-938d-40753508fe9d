{"version": 3, "sources": ["../../../../src/server/normalizers/request/prefix.ts"], "sourcesContent": ["import type { Normalizer } from '../normalizer'\n\nexport class PrefixPathnameNormalizer implements Normalizer {\n  constructor(private readonly prefix: string) {\n    if (prefix.endsWith('/')) {\n      throw new Error(\n        `PrefixPathnameNormalizer: prefix \"${prefix}\" should not end with a slash`\n      )\n    }\n  }\n\n  public match(pathname: string) {\n    // If the pathname doesn't start with the prefix, we don't match.\n    if (pathname !== this.prefix && !pathname.startsWith(this.prefix + '/')) {\n      return false\n    }\n\n    return true\n  }\n\n  public normalize(pathname: string, matched?: boolean): string {\n    // If we're not matched and we don't match, we don't need to normalize.\n    if (!matched && !this.match(pathname)) return pathname\n\n    if (pathname.length === this.prefix.length) {\n      return '/'\n    }\n\n    return pathname.substring(this.prefix.length)\n  }\n}\n"], "names": ["PrefixPathnameNormalizer", "constructor", "prefix", "endsWith", "Error", "match", "pathname", "startsWith", "normalize", "matched", "length", "substring"], "mappings": "AAEA,OAAO,MAAMA;IACXC,YAAY,AAAiBC,MAAc,CAAE;aAAhBA,SAAAA;QAC3B,IAAIA,OAAOC,QAAQ,CAAC,MAAM;YACxB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,kCAAkC,EAAEF,OAAO,6BAA6B,CAAC,GADtE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEOG,MAAMC,QAAgB,EAAE;QAC7B,iEAAiE;QACjE,IAAIA,aAAa,IAAI,CAACJ,MAAM,IAAI,CAACI,SAASC,UAAU,CAAC,IAAI,CAACL,MAAM,GAAG,MAAM;YACvE,OAAO;QACT;QAEA,OAAO;IACT;IAEOM,UAAUF,QAAgB,EAAEG,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,WAAW,OAAOA;QAE9C,IAAIA,SAASI,MAAM,KAAK,IAAI,CAACR,MAAM,CAACQ,MAAM,EAAE;YAC1C,OAAO;QACT;QAEA,OAAOJ,SAASK,SAAS,CAAC,IAAI,CAACT,MAAM,CAACQ,MAAM;IAC9C;AACF"}