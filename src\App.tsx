import React, { useState } from 'react'
import LoginPage from './components/LoginPage'
import Dashboard from './components/Dashboard'
import './App.css'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<{ name: string; username: string } | null>(null)

  const handleLogin = (username: string, password: string) => {
    // Simple authentication logic - in real app, this would be an API call
    if (username && password) {
      setIsAuthenticated(true)
      setUser({ name: '<PERSON>', username })
    }
  }

  const handleLogout = () => {
    setIsAuthenticated(false)
    setUser(null)
  }

  return (
    <div className="min-h-screen">
      {!isAuthenticated ? (
        <LoginPage onLogin={handleLogin} />
      ) : (
        <Dashboard user={user} onLogout={handleLogout} />
      )}
    </div>
  )
}

export default App
