{"version": 3, "sources": ["../../../src/server/normalizers/prefixing-normalizer.ts"], "sourcesContent": ["import path from '../../shared/lib/isomorphic/path'\nimport type { Normalizer } from './normalizer'\n\nexport class PrefixingNormalizer implements Normalizer {\n  private readonly prefix: string\n\n  constructor(...prefixes: ReadonlyArray<string>) {\n    this.prefix = path.posix.join(...prefixes)\n  }\n\n  public normalize(pathname: string): string {\n    return path.posix.join(this.prefix, pathname)\n  }\n}\n"], "names": ["path", "PrefixingNormalizer", "constructor", "prefixes", "prefix", "posix", "join", "normalize", "pathname"], "mappings": "AAAA,OAAOA,UAAU,mCAAkC;AAGnD,OAAO,MAAMC;IAGXC,YAAY,GAAGC,QAA+B,CAAE;QAC9C,IAAI,CAACC,MAAM,GAAGJ,KAAKK,KAAK,CAACC,IAAI,IAAIH;IACnC;IAEOI,UAAUC,QAAgB,EAAU;QACzC,OAAOR,KAAKK,KAAK,CAACC,IAAI,CAAC,IAAI,CAACF,MAAM,EAAEI;IACtC;AACF"}