"use strict";
// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOption = exports.hasOption = exports.clearExtension = exports.setExtension = exports.getExtension = exports.hasExtension = exports.mergeFromBinary = exports.fromBinary = exports.toBinary = void 0;
__exportStar(require("./types.js"), exports);
__exportStar(require("./is-message.js"), exports);
__exportStar(require("./create.js"), exports);
__exportStar(require("./clone.js"), exports);
__exportStar(require("./descriptors.js"), exports);
__exportStar(require("./equals.js"), exports);
__exportStar(require("./fields.js"), exports);
__exportStar(require("./registry.js"), exports);
var to_binary_js_1 = require("./to-binary.js");
Object.defineProperty(exports, "toBinary", { enumerable: true, get: function () { return to_binary_js_1.toBinary; } });
var from_binary_js_1 = require("./from-binary.js");
Object.defineProperty(exports, "fromBinary", { enumerable: true, get: function () { return from_binary_js_1.fromBinary; } });
Object.defineProperty(exports, "mergeFromBinary", { enumerable: true, get: function () { return from_binary_js_1.mergeFromBinary; } });
__exportStar(require("./to-json.js"), exports);
__exportStar(require("./from-json.js"), exports);
__exportStar(require("./merge.js"), exports);
var extensions_js_1 = require("./extensions.js");
Object.defineProperty(exports, "hasExtension", { enumerable: true, get: function () { return extensions_js_1.hasExtension; } });
Object.defineProperty(exports, "getExtension", { enumerable: true, get: function () { return extensions_js_1.getExtension; } });
Object.defineProperty(exports, "setExtension", { enumerable: true, get: function () { return extensions_js_1.setExtension; } });
Object.defineProperty(exports, "clearExtension", { enumerable: true, get: function () { return extensions_js_1.clearExtension; } });
Object.defineProperty(exports, "hasOption", { enumerable: true, get: function () { return extensions_js_1.hasOption; } });
Object.defineProperty(exports, "getOption", { enumerable: true, get: function () { return extensions_js_1.getOption; } });
__exportStar(require("./proto-int64.js"), exports);
