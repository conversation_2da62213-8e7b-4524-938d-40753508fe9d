{"version": 3, "sources": ["../../../../src/server/route-modules/pages/module.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type {\n  GetServerSideProps,\n  GetStaticPaths,\n  GetStaticProps,\n  NextComponentType,\n  PageConfig,\n} from '../../../types'\nimport type { PagesRouteDefinition } from '../../route-definitions/pages-route-definition'\nimport type { NextParsedUrlQuery } from '../../request-meta'\nimport type {\n  PagesRenderContext,\n  PagesSharedContext,\n  RenderOpts,\n} from '../../render'\nimport type RenderResult from '../../render-result'\nimport type { AppType, DocumentType } from '../../../shared/lib/utils'\n\nimport {\n  RouteModule,\n  type RouteModuleHandleContext,\n  type RouteModuleOptions,\n} from '../route-module'\nimport { renderToHTMLImpl, renderToHTML } from '../../render'\nimport * as vendoredContexts from './vendored/contexts/entrypoints'\n\n/**\n * The PagesModule is the type of the module exported by the bundled pages\n * module.\n */\nexport type PagesModule = typeof import('../../../build/templates/pages')\n\n/**\n * The userland module for a page. This is the module that is exported from the\n * page file that contains the page component, page config, and any page data\n * fetching functions.\n */\nexport type PagesUserlandModule = {\n  /**\n   * The exported page component.\n   */\n  readonly default: NextComponentType\n\n  /**\n   * The exported page config.\n   */\n  readonly config?: PageConfig\n\n  /**\n   * The exported `getStaticProps` function.\n   */\n  readonly getStaticProps?: GetStaticProps\n\n  /**\n   * The exported `getStaticPaths` function.\n   */\n  readonly getStaticPaths?: GetStaticPaths\n\n  /**\n   * The exported `getServerSideProps` function.\n   */\n  readonly getServerSideProps?: GetServerSideProps\n}\n\n/**\n * The components that are used to render a page. These aren't tied to the\n * specific page being rendered, but rather are the components that are used to\n * render all pages.\n */\ntype PagesComponents = {\n  /**\n   * The `App` component. This could be exported by a user's custom `_app` page\n   * file, or it could be the default `App` component.\n   */\n  readonly App: AppType\n\n  /**\n   * The `Document` component. This could be exported by a user's custom\n   * `_document` page file, or it could be the default `Document` component.\n   */\n  readonly Document: DocumentType\n}\n\nexport interface PagesRouteModuleOptions\n  extends RouteModuleOptions<PagesRouteDefinition, PagesUserlandModule> {\n  readonly components: PagesComponents\n}\n\n/**\n * AppRouteRouteHandlerContext is the context that is passed to the route\n * handler for app routes.\n */\nexport interface PagesRouteHandlerContext extends RouteModuleHandleContext {\n  /**\n   * The page for the given route.\n   */\n  page: string\n\n  /**\n   * The parsed URL query for the given request.\n   */\n  query: NextParsedUrlQuery\n\n  /**\n   * The shared context used for all page renders.\n   */\n  sharedContext: PagesSharedContext\n\n  /**\n   * The context for the given request.\n   */\n  renderContext: PagesRenderContext\n\n  /**\n   * The arguments for the given request.\n  /**\n   * The RenderOpts for the given request which include the specific modules to\n   * use for rendering.\n   */\n  // TODO: (wyattjoh) break this out into smaller parts, it currently includes the userland components\n  renderOpts: Omit<RenderOpts, 'Document' | 'App'>\n}\n\nexport class PagesRouteModule extends RouteModule<\n  PagesRouteDefinition,\n  PagesUserlandModule\n> {\n  private readonly components: PagesComponents\n\n  constructor(options: PagesRouteModuleOptions) {\n    super(options)\n\n    this.components = options.components\n  }\n\n  public render(\n    req: IncomingMessage,\n    res: ServerResponse,\n    context: PagesRouteHandlerContext\n  ): Promise<RenderResult> {\n    return renderToHTMLImpl(\n      req,\n      res,\n      context.page,\n      context.query,\n      context.renderOpts,\n      {\n        App: this.components.App,\n        Document: this.components.Document,\n      },\n      context.sharedContext,\n      context.renderContext\n    )\n  }\n}\n\nconst vendored = {\n  contexts: vendoredContexts,\n}\n\n// needed for the static build\nexport { renderToHTML, vendored }\n\nexport default PagesRouteModule\n"], "names": ["RouteModule", "renderToHTMLImpl", "renderToHTML", "vendoredContexts", "PagesRouteModule", "constructor", "options", "components", "render", "req", "res", "context", "page", "query", "renderOpts", "App", "Document", "sharedContext", "renderContext", "vendored", "contexts"], "mappings": "AAkBA,SACEA,WAAW,QAGN,kBAAiB;AACxB,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,eAAc;AAC7D,YAAYC,sBAAsB,kCAAiC;AAmGnE,OAAO,MAAMC,yBAAyBJ;IAMpCK,YAAYC,OAAgC,CAAE;QAC5C,KAAK,CAACA;QAEN,IAAI,CAACC,UAAU,GAAGD,QAAQC,UAAU;IACtC;IAEOC,OACLC,GAAoB,EACpBC,GAAmB,EACnBC,OAAiC,EACV;QACvB,OAAOV,iBACLQ,KACAC,KACAC,QAAQC,IAAI,EACZD,QAAQE,KAAK,EACbF,QAAQG,UAAU,EAClB;YACEC,KAAK,IAAI,CAACR,UAAU,CAACQ,GAAG;YACxBC,UAAU,IAAI,CAACT,UAAU,CAACS,QAAQ;QACpC,GACAL,QAAQM,aAAa,EACrBN,QAAQO,aAAa;IAEzB;AACF;AAEA,MAAMC,WAAW;IACfC,UAAUjB;AACZ;AAEA,8BAA8B;AAC9B,SAASD,YAAY,EAAEiB,QAAQ,GAAE;AAEjC,eAAef,iBAAgB"}