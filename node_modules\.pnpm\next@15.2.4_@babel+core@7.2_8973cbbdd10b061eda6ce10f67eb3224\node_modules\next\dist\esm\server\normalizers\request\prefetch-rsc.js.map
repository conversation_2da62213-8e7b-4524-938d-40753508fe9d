{"version": 3, "sources": ["../../../../src/server/normalizers/request/prefetch-rsc.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport { RSC_PREFETCH_SUFFIX } from '../../../lib/constants'\nimport { SuffixPathnameNormalizer } from './suffix'\n\nexport class PrefetchRSCPathnameNormalizer\n  extends SuffixPath<PERSON>Normalizer\n  implements PathnameNormalizer\n{\n  constructor() {\n    super(RSC_PREFETCH_SUFFIX)\n  }\n\n  public match(pathname: string): boolean {\n    if (pathname === '/__index' + RSC_PREFETCH_SUFFIX) {\n      return true\n    }\n\n    return super.match(pathname)\n  }\n\n  public normalize(pathname: string, matched?: boolean): string {\n    if (pathname === '/__index' + RSC_PREFETCH_SUFFIX) {\n      return '/'\n    }\n\n    return super.normalize(pathname, matched)\n  }\n}\n"], "names": ["RSC_PREFETCH_SUFFIX", "SuffixPathnameNormalizer", "PrefetchRSCPathnameNormalizer", "constructor", "match", "pathname", "normalize", "matched"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,wBAAwB,QAAQ,WAAU;AAEnD,OAAO,MAAMC,sCACHD;IAGRE,aAAc;QACZ,KAAK,CAACH;IACR;IAEOI,MAAMC,QAAgB,EAAW;QACtC,IAAIA,aAAa,aAAaL,qBAAqB;YACjD,OAAO;QACT;QAEA,OAAO,KAAK,CAACI,MAAMC;IACrB;IAEOC,UAAUD,QAAgB,EAAEE,OAAiB,EAAU;QAC5D,IAAIF,aAAa,aAAaL,qBAAqB;YACjD,OAAO;QACT;QAEA,OAAO,KAAK,CAACM,UAAUD,UAAUE;IACnC;AACF"}