"use strict";
// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.FieldMaskSchema = exports.file_google_protobuf_field_mask = void 0;
const file_js_1 = require("../../../../codegenv2/file.js");
const message_js_1 = require("../../../../codegenv2/message.js");
/**
 * Describes the file google/protobuf/field_mask.proto.
 */
exports.file_google_protobuf_field_mask = (0, file_js_1.fileDesc)("CiBnb29nbGUvcHJvdG9idWYvZmllbGRfbWFzay5wcm90bxIPZ29vZ2xlLnByb3RvYnVmIhoKCUZpZWxkTWFzaxINCgVwYXRocxgBIAMoCUKFAQoTY29tLmdvb2dsZS5wcm90b2J1ZkIORmllbGRNYXNrUHJvdG9QAVoyZ29vZ2xlLmdvbGFuZy5vcmcvcHJvdG9idWYvdHlwZXMva25vd24vZmllbGRtYXNrcGL4AQGiAgNHUEKqAh5Hb29nbGUuUHJvdG9idWYuV2VsbEtub3duVHlwZXNiBnByb3RvMw");
/**
 * Describes the message google.protobuf.FieldMask.
 * Use `create(FieldMaskSchema)` to create a new message.
 */
exports.FieldMaskSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_field_mask, 0);
