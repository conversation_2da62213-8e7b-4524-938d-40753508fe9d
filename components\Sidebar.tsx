'use client'

import React from 'react'
import { LayoutDashboard, BookOpen, Users, Settings, FileText } from 'lucide-react'

interface SidebarProps {
  activeSection: string
  onSectionChange: (section: string) => void
}

const Sidebar: React.FC<SidebarProps> = ({ activeSection, onSectionChange }) => {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'daybook', label: 'Daybook', icon: BookOpen },
    { id: 'ledger', label: 'Ledger', icon: FileText },
    { id: 'customers', label: 'Customers', icon: Users },
    { id: 'settings', label: 'Settings', icon: Settings },
  ]

  return (
    <div className="w-64 h-full shadow-lg" style={{ backgroundColor: '#1A237E' }}>
      <div className="p-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
            <span className="text-indigo-900 text-xl font-bold">S</span>
          </div>
          <div>
            <h2 className="text-white text-xl font-bold">Sarafi</h2>
            <p className="text-indigo-200 text-sm">Money Exchange</p>
          </div>
        </div>
      </div>

      <nav className="mt-8">
        <ul className="space-y-2 px-4">
          {menuItems.map((item) => {
            const Icon = item.icon
            const isActive = activeSection === item.id
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => onSectionChange(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors duration-200 ${
                    isActive
                      ? 'bg-indigo-700 text-white'
                      : 'text-indigo-200 hover:bg-indigo-700 hover:text-white'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{item.label}</span>
                </button>
              </li>
            )
          })}
        </ul>
      </nav>

      <div className="absolute bottom-0 left-0 right-0 p-4">
        <div className="bg-indigo-800 rounded-lg p-4">
          <p className="text-indigo-200 text-sm">Need help?</p>
          <p className="text-white text-sm font-medium">Contact Support</p>
        </div>
      </div>
    </div>
  )
}

export default Sidebar
