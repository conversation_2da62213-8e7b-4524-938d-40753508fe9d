hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.3.3':
    '@adobe/css-tools': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@bufbuild/protobuf@2.6.3':
    '@bufbuild/protobuf': private
  '@date-fns/tz@1.2.0':
    '@date-fns/tz': private
  '@esbuild/aix-ppc64@0.25.8':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.8':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.8':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.8':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.8':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.8':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.8':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.8':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.8':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.8':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.8':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.8':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.8':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.8':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.8':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.8':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.8':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.8':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.8':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.8':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.8':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.8':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.8':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.8':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.8':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.3':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.5(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@next/env@15.2.4':
    '@next/env': private
  '@next/swc-darwin-arm64@15.2.4':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.2.4':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.2.4':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.2.4':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.2.4':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.2.4':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.2.4':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.2.4':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.1':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.3(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.1(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.4(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.3(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.2(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@19.1.9)(react@19.1.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.1(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.46.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.46.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.46.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.46.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.46.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.46.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.46.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.46.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.46.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.46.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.46.2':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.46.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.46.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.46.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.46.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.46.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.46.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.46.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/estree@1.0.8':
    '@types/estree': private
  acorn@8.15.0:
    acorn: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  aria-hidden@1.2.6:
    aria-hidden: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  buffer-builder@0.2.0:
    buffer-builder: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  chokidar@4.0.3:
    chokidar: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colorjs.io@0.5.2:
    colorjs.io: private
  commander@2.20.3:
    commander: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@2.0.6:
    copy-anything: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  date-fns-jalali@4.1.0-0:
    date-fns-jalali: private
  debug@4.4.1:
    debug: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  dom-helpers@5.2.1:
    dom-helpers: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.197:
    electron-to-chromium: private
  embla-carousel-reactive-utils@8.5.1(embla-carousel@8.5.1):
    embla-carousel-reactive-utils: private
  embla-carousel@8.5.1:
    embla-carousel: private
  emoji-regex@8.0.0:
    emoji-regex: private
  errno@0.1.8:
    errno: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  eventemitter3@4.0.7:
    eventemitter3: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-glob@3.3.3:
    fast-glob: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  fill-range@7.1.1:
    fill-range: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-nonce@1.0.1:
    get-nonce: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  iconv-lite@0.6.3:
    iconv-lite: private
  image-size@0.5.5:
    image-size: private
  immutable@5.1.3:
    immutable: private
  inherits@2.0.3:
    inherits: private
  internmap@2.0.3:
    internmap: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-what@3.14.1:
    is-what: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json5@2.2.3:
    json5: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  make-dir@2.1.0:
    make-dir: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime@1.6.0:
    mime: private
  minimatch@9.0.5:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  needle@3.3.1:
    needle: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parse-node-version@1.0.1:
    parse-node-version: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pify@4.0.1:
    pify: private
  pirates@4.0.7:
    pirates: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  process@0.11.10:
    process: private
  prop-types@15.8.1:
    prop-types: private
  prr@1.0.1:
    prr: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.9)(react@19.1.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@19.1.9)(react@19.1.1):
    react-remove-scroll: private
  react-smooth@4.0.4(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    react-smooth: private
  react-style-singleton@2.2.3(@types/react@19.1.9)(react@19.1.1):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    react-transition-group: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  recharts-scale@0.4.5:
    recharts-scale: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rollup@4.46.2:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sass-embedded-all-unknown@1.90.0:
    sass-embedded-all-unknown: private
  sass-embedded-android-arm64@1.90.0:
    sass-embedded-android-arm64: private
  sass-embedded-android-arm@1.90.0:
    sass-embedded-android-arm: private
  sass-embedded-android-riscv64@1.90.0:
    sass-embedded-android-riscv64: private
  sass-embedded-android-x64@1.90.0:
    sass-embedded-android-x64: private
  sass-embedded-darwin-arm64@1.90.0:
    sass-embedded-darwin-arm64: private
  sass-embedded-darwin-x64@1.90.0:
    sass-embedded-darwin-x64: private
  sass-embedded-linux-arm64@1.90.0:
    sass-embedded-linux-arm64: private
  sass-embedded-linux-arm@1.90.0:
    sass-embedded-linux-arm: private
  sass-embedded-linux-musl-arm64@1.90.0:
    sass-embedded-linux-musl-arm64: private
  sass-embedded-linux-musl-arm@1.90.0:
    sass-embedded-linux-musl-arm: private
  sass-embedded-linux-musl-riscv64@1.90.0:
    sass-embedded-linux-musl-riscv64: private
  sass-embedded-linux-musl-x64@1.90.0:
    sass-embedded-linux-musl-x64: private
  sass-embedded-linux-riscv64@1.90.0:
    sass-embedded-linux-riscv64: private
  sass-embedded-linux-x64@1.90.0:
    sass-embedded-linux-x64: private
  sass-embedded-unknown-all@1.90.0:
    sass-embedded-unknown-all: private
  sass-embedded-win32-arm64@1.90.0:
    sass-embedded-win32-arm64: private
  sass-embedded-win32-x64@1.90.0:
    sass-embedded-win32-x64: private
  sax@1.4.1:
    sax: private
  scheduler@0.26.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  styled-jsx@5.1.6(@babel/core@7.28.0)(react@19.1.1):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@8.1.1:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  sync-child-process@1.0.2:
    sync-child-process: private
  sync-message-port@1.1.3:
    sync-message-port: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  undici-types@6.21.0:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  use-callback-ref@1.3.3(@types/react@19.1.9)(react@19.1.1):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.1.9)(react@19.1.1):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.10.4:
    util: private
  varint@6.0.0:
    varint: private
  victory-vendor@36.9.2:
    victory-vendor: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  yallist@3.1.1:
    yallist: private
ignoredBuilds:
  - '@parcel/watcher'
  - esbuild
  - sharp
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Wed, 06 Aug 2025 14:29:51 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.4.5'
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@next/swc-darwin-arm64@15.2.4'
  - '@next/swc-darwin-x64@15.2.4'
  - '@next/swc-linux-arm64-gnu@15.2.4'
  - '@next/swc-linux-arm64-musl@15.2.4'
  - '@next/swc-linux-x64-gnu@15.2.4'
  - '@next/swc-linux-x64-musl@15.2.4'
  - '@next/swc-win32-arm64-msvc@15.2.4'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - sass-embedded-all-unknown@1.90.0
  - sass-embedded-android-arm64@1.90.0
  - sass-embedded-android-arm@1.90.0
  - sass-embedded-android-riscv64@1.90.0
  - sass-embedded-android-x64@1.90.0
  - sass-embedded-darwin-arm64@1.90.0
  - sass-embedded-darwin-x64@1.90.0
  - sass-embedded-linux-arm64@1.90.0
  - sass-embedded-linux-arm@1.90.0
  - sass-embedded-linux-musl-arm64@1.90.0
  - sass-embedded-linux-musl-arm@1.90.0
  - sass-embedded-linux-musl-riscv64@1.90.0
  - sass-embedded-linux-musl-x64@1.90.0
  - sass-embedded-linux-riscv64@1.90.0
  - sass-embedded-linux-x64@1.90.0
  - sass-embedded-unknown-all@1.90.0
  - sass-embedded-win32-arm64@1.90.0
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\Sarafi\node_modules\.pnpm
virtualStoreDirMaxLength: 60
