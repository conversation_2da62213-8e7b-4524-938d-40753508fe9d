{"version": 3, "sources": ["../../../src/server/request/headers.ts"], "sourcesContent": ["import {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * In this version of Next.js `headers()` returns a Promise however you can still reference the properties of the underlying Headers instance\n * synchronously to facilitate migration. The `UnsafeUnwrappedHeaders` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `headers()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedHeaders` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `headers()` value can be awaited or you should call `headers()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedHeaders` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `headers()` will only return a Promise and you will not be able to access the underlying Headers instance\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedHeaders` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedHeaders = ReadonlyHeaders\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedExoticHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We don't track dynamic access here because access will be tracked when you access\n        // one of the properties of the headers object.\n        return makeDynamicallyTrackedExoticHeaders(\n          workStore.route,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We are prerendering with PPR. We need track dynamic access here eagerly\n        // to keep continuity with how headers has worked in PPR without dynamicIO.\n        // TODO consider switching the semantic to throw on property access instead\n        postponeWithTracking(\n          workStore.route,\n          'headers',\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We are in a legacy static generation mode while prerendering\n        // We track dynamic access here so we don't need to wrap the headers in\n        // individual property access tracking.\n        throwToInterruptStaticGeneration('headers', workStore, workUnitStore)\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using headers inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  const requestStore = getExpectedRequestStore('headers')\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    return makeUntrackedExoticHeadersWithDevWarnings(\n      requestStore.headers,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticHeaders(requestStore.headers)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeDynamicallyTrackedExoticHeaders(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`headers()[Symbol.iterator]()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: underlyingHeaders.append.bind(underlyingHeaders),\n    },\n    delete: {\n      value: underlyingHeaders.delete.bind(underlyingHeaders),\n    },\n    get: {\n      value: underlyingHeaders.get.bind(underlyingHeaders),\n    },\n    has: {\n      value: underlyingHeaders.has.bind(underlyingHeaders),\n    },\n    set: {\n      value: underlyingHeaders.set.bind(underlyingHeaders),\n    },\n    getSetCookie: {\n      value: underlyingHeaders.getSetCookie.bind(underlyingHeaders),\n    },\n    forEach: {\n      value: underlyingHeaders.forEach.bind(underlyingHeaders),\n    },\n    keys: {\n      value: underlyingHeaders.keys.bind(underlyingHeaders),\n    },\n    values: {\n      value: underlyingHeaders.values.bind(underlyingHeaders),\n    },\n    entries: {\n      value: underlyingHeaders.entries.bind(underlyingHeaders),\n    },\n    [Symbol.iterator]: {\n      value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders),\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = new Promise<ReadonlyHeaders>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingHeaders))\n  )\n\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.append.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.delete.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.get.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.has.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.set.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.getSetCookie.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        syncIODev(route, expression)\n        return underlyingHeaders.forEach.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.keys.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.values.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.entries.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...headers()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingHeaders[Symbol.iterator].apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'string' ? `'${arg}'` : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\ntype HeadersExtensions = {\n  [K in keyof ReadonlyHeaders]: unknown\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "workAsyncStorage", "getExpectedRequestStore", "workUnitAsyncStorage", "postponeWithTracking", "abortAndThrowOnSynchronousRequestDataAccess", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "trackSynchronousRequestDataAccessInDev", "StaticGenBailoutError", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "scheduleImmediate", "isRequestAPICallableInsideAfter", "headers", "workStore", "getStore", "workUnitStore", "phase", "Error", "route", "forceStatic", "underlyingHeaders", "seal", "Headers", "makeUntrackedExoticHeaders", "type", "dynamicShouldError", "makeDynamicallyTrackedExoticHeaders", "dynamicTracking", "requestStore", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeUntrackedExoticHeadersWithDevWarnings", "CachedHeaders", "WeakMap", "prerenderStore", "cachedHeaders", "get", "promise", "renderSignal", "set", "Object", "defineProperties", "append", "value", "expression", "describeNameArg", "arguments", "error", "createHeadersAccessError", "delete", "_delete", "has", "getSetCookie", "for<PERSON>ach", "keys", "values", "entries", "Symbol", "iterator", "Promise", "resolve", "bind", "syncIODev", "apply", "arg", "prerenderPhase", "warnForSyncAccess", "prefix"], "mappings": "AAAA,SACEA,cAAc,QAET,yCAAwC;AAC/C,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,uBAAuB,QAAQ,iDAAgD;AACxF,SACEC,oBAAoB,QAEf,iDAAgD;AACvD,SACEC,oBAAoB,EACpBC,2CAA2C,EAC3CC,gCAAgC,EAChCC,+BAA+B,EAC/BC,sCAAsC,QACjC,kCAAiC;AACxC,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,iBAAiB,QAAQ,sBAAqB;AACvD,SAASC,+BAA+B,QAAQ,UAAS;AAyBzD;;;;;;;;CAQC,GACD,OAAO,SAASC;IACd,MAAMC,YAAYd,iBAAiBe,QAAQ;IAC3C,MAAMC,gBAAgBd,qBAAqBa,QAAQ;IAEnD,IAAID,WAAW;QACb,IACEE,iBACAA,cAAcC,KAAK,KAAK,WACxB,CAACL,mCACD;YACA,MAAM,qBAEL,CAFK,IAAIM,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,yOAAyO,CAAC,GAD/P,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,UAAUM,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBtB,eAAeuB,IAAI,CAAC,IAAIC,QAAQ,CAAC;YAC3D,OAAOC,2BAA2BH;QACpC;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcS,IAAI,KAAK,SAAS;gBAClC,MAAM,qBAEL,CAFK,IAAIP,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIH,cAAcS,IAAI,KAAK,kBAAkB;gBAClD,MAAM,qBAEL,CAFK,IAAIP,MACR,CAAC,MAAM,EAAEJ,UAAUK,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIL,UAAUY,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIlB,sBACR,CAAC,MAAM,EAAEM,UAAUK,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIH,eAAe;YACjB,IAAIA,cAAcS,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,oFAAoF;gBACpF,+CAA+C;gBAC/C,OAAOE,oCACLb,UAAUK,KAAK,EACfH;YAEJ,OAAO,IAAIA,cAAcS,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,0EAA0E;gBAC1E,2EAA2E;gBAC3E,2EAA2E;gBAC3EtB,qBACEW,UAAUK,KAAK,EACf,WACAH,cAAcY,eAAe;YAEjC,OAAO,IAAIZ,cAAcS,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,+DAA+D;gBAC/D,uEAAuE;gBACvE,uCAAuC;gBACvCpB,iCAAiC,WAAWS,WAAWE;YACzD;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFV,gCAAgCQ,WAAWE;IAC7C;IAEA,MAAMa,eAAe5B,wBAAwB;IAC7C,IAAI6B,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAAClB,6BAAAA,UAAWmB,iBAAiB,GAAE;QAC3E,OAAOC,0CACLL,aAAahB,OAAO,EACpBC,6BAAAA,UAAWK,KAAK;IAEpB,OAAO;QACL,OAAOK,2BAA2BK,aAAahB,OAAO;IACxD;AACF;AAGA,MAAMsB,gBAAgB,IAAIC;AAE1B,SAAST,oCACPR,KAAa,EACbkB,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAU/B,mBACd4B,eAAeI,YAAY,EAC3B;IAEFN,cAAcO,GAAG,CAACL,gBAAgBG;IAElCG,OAAOC,gBAAgB,CAACJ,SAAS;QAC/BK,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAME,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChF,MAAMC,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAe,QAAQ;YACNN,OAAO,SAASO;gBACd,MAAMN,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3E,MAAMC,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAE,KAAK;YACHO,OAAO,SAASP;gBACd,MAAMQ,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxE,MAAMC,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAiB,KAAK;YACHR,OAAO,SAASQ;gBACd,MAAMP,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxE,MAAMC,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAK,KAAK;YACHI,OAAO,SAASJ;gBACd,MAAMK,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7E,MAAMC,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAkB,cAAc;YACZT,OAAO,SAASS;gBACd,MAAMR,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAmB,SAAS;YACPV,OAAO,SAASU;gBACd,MAAMT,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAoB,MAAM;YACJX,OAAO,SAASW;gBACd,MAAMV,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAqB,QAAQ;YACNZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACAsB,SAAS;YACPb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;QACA,CAACuB,OAAOC,QAAQ,CAAC,EAAE;YACjBf,OAAO;gBACL,MAAMC,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBhC,OAAO4B;gBAC9C3C,4CACEe,OACA4B,YACAG,OACAb;YAEJ;QACF;IACF;IAEA,OAAOG;AACT;AAEA,SAAShB,2BACPH,iBAAkC;IAElC,MAAMiB,gBAAgBH,cAAcI,GAAG,CAAClB;IACxC,IAAIiB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUsB,QAAQC,OAAO,CAAC1C;IAChCc,cAAcO,GAAG,CAACrB,mBAAmBmB;IAErCG,OAAOC,gBAAgB,CAACJ,SAAS;QAC/BK,QAAQ;YACNC,OAAOzB,kBAAkBwB,MAAM,CAACmB,IAAI,CAAC3C;QACvC;QACA+B,QAAQ;YACNN,OAAOzB,kBAAkB+B,MAAM,CAACY,IAAI,CAAC3C;QACvC;QACAkB,KAAK;YACHO,OAAOzB,kBAAkBkB,GAAG,CAACyB,IAAI,CAAC3C;QACpC;QACAiC,KAAK;YACHR,OAAOzB,kBAAkBiC,GAAG,CAACU,IAAI,CAAC3C;QACpC;QACAqB,KAAK;YACHI,OAAOzB,kBAAkBqB,GAAG,CAACsB,IAAI,CAAC3C;QACpC;QACAkC,cAAc;YACZT,OAAOzB,kBAAkBkC,YAAY,CAACS,IAAI,CAAC3C;QAC7C;QACAmC,SAAS;YACPV,OAAOzB,kBAAkBmC,OAAO,CAACQ,IAAI,CAAC3C;QACxC;QACAoC,MAAM;YACJX,OAAOzB,kBAAkBoC,IAAI,CAACO,IAAI,CAAC3C;QACrC;QACAqC,QAAQ;YACNZ,OAAOzB,kBAAkBqC,MAAM,CAACM,IAAI,CAAC3C;QACvC;QACAsC,SAAS;YACPb,OAAOzB,kBAAkBsC,OAAO,CAACK,IAAI,CAAC3C;QACxC;QACA,CAACuC,OAAOC,QAAQ,CAAC,EAAE;YACjBf,OAAOzB,iBAAiB,CAACuC,OAAOC,QAAQ,CAAC,CAACG,IAAI,CAAC3C;QACjD;IACF;IAEA,OAAOmB;AACT;AAEA,SAASN,0CACPb,iBAAkC,EAClCF,KAAc;IAEd,MAAMmB,gBAAgBH,cAAcI,GAAG,CAAClB;IACxC,IAAIiB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAU,IAAIsB,QAAyB,CAACC,UAC5CpD,kBAAkB,IAAMoD,QAAQ1C;IAGlCc,cAAcO,GAAG,CAACrB,mBAAmBmB;IAErCG,OAAOC,gBAAgB,CAACJ,SAAS;QAC/BK,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAME,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChFgB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBwB,MAAM,CAACqB,KAAK,CACnC7C,mBACA4B;YAEJ;QACF;QACAG,QAAQ;YACNN,OAAO,SAASO;gBACd,MAAMN,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3EgB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkB+B,MAAM,CAACc,KAAK,CACnC7C,mBACA4B;YAEJ;QACF;QACAV,KAAK;YACHO,OAAO,SAASP;gBACd,MAAMQ,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEgB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBkB,GAAG,CAAC2B,KAAK,CAAC7C,mBAAmB4B;YACxD;QACF;QACAK,KAAK;YACHR,OAAO,SAASQ;gBACd,MAAMP,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEgB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBiC,GAAG,CAACY,KAAK,CAAC7C,mBAAmB4B;YACxD;QACF;QACAP,KAAK;YACHI,OAAO,SAASJ;gBACd,MAAMK,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7EgB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBqB,GAAG,CAACwB,KAAK,CAAC7C,mBAAmB4B;YACxD;QACF;QACAM,cAAc;YACZT,OAAO,SAASS;gBACd,MAAMR,aAAa;gBACnBkB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBkC,YAAY,CAACW,KAAK,CACzC7C,mBACA4B;YAEJ;QACF;QACAO,SAAS;YACPV,OAAO,SAASU;gBACd,MAAMT,aAAa;gBACnBkB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBmC,OAAO,CAACU,KAAK,CACpC7C,mBACA4B;YAEJ;QACF;QACAQ,MAAM;YACJX,OAAO,SAASW;gBACd,MAAMV,aAAa;gBACnBkB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBoC,IAAI,CAACS,KAAK,CAAC7C,mBAAmB4B;YACzD;QACF;QACAS,QAAQ;YACNZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnBkB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBqC,MAAM,CAACQ,KAAK,CACnC7C,mBACA4B;YAEJ;QACF;QACAU,SAAS;YACPb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnBkB,UAAU9C,OAAO4B;gBACjB,OAAO1B,kBAAkBsC,OAAO,CAACO,KAAK,CACpC7C,mBACA4B;YAEJ;QACF;QACA,CAACW,OAAOC,QAAQ,CAAC,EAAE;YACjBf,OAAO;gBACL,MAAMC,aAAa;gBACnBkB,UAAU9C,OAAO4B;gBACjB,OAAO1B,iBAAiB,CAACuC,OAAOC,QAAQ,CAAC,CAACK,KAAK,CAC7C7C,mBACA4B;YAEJ;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASQ,gBAAgBmB,GAAY;IACnC,OAAO,OAAOA,QAAQ,WAAW,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GAAG;AAChD;AAEA,SAASF,UAAU9C,KAAyB,EAAE4B,UAAkB;IAC9D,MAAM/B,gBAAgBd,qBAAqBa,QAAQ;IACnD,IACEC,iBACAA,cAAcS,IAAI,KAAK,aACvBT,cAAcoD,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMvC,eAAeb;QACrBT,uCAAuCsB;IACzC;IACA,gCAAgC;IAChCwC,kBAAkBlD,OAAO4B;AAC3B;AAEA,MAAMsB,oBAAoB3D,4CACxByC;AAGF,SAASA,yBACPhC,KAAyB,EACzB4B,UAAkB;IAElB,MAAMuB,SAASnD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAGoD,OAAO,KAAK,EAAEvB,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF"}