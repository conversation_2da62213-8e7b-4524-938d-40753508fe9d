{"version": 3, "sources": ["../../../src/server/web/get-edge-preview-props.ts"], "sourcesContent": ["/**\n * In edge runtime, these props directly accessed from environment variables.\n *   - local: env vars will be injected through edge-runtime as runtime env vars\n *   - deployment: env vars will be replaced by edge build pipeline\n */\nexport function getEdgePreviewProps() {\n  return {\n    previewModeId:\n      process.env.NODE_ENV === 'production'\n        ? process.env.__NEXT_PREVIEW_MODE_ID!\n        : 'development-id',\n    previewModeSigningKey: process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY || '',\n    previewModeEncryptionKey:\n      process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY || '',\n  }\n}\n"], "names": ["getEdgePreviewProps", "previewModeId", "process", "env", "NODE_ENV", "__NEXT_PREVIEW_MODE_ID", "previewModeSigningKey", "__NEXT_PREVIEW_MODE_SIGNING_KEY", "previewModeEncryptionKey", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY"], "mappings": "AAAA;;;;CAIC,GACD,OAAO,SAASA;IACd,OAAO;QACLC,eACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBF,QAAQC,GAAG,CAACE,sBAAsB,GAClC;QACNC,uBAAuBJ,QAAQC,GAAG,CAACI,+BAA+B,IAAI;QACtEC,0BACEN,QAAQC,GAAG,CAACM,kCAAkC,IAAI;IACtD;AACF"}