{"version": 3, "sources": ["../../../src/server/use-cache/use-cache-wrapper.ts"], "sourcesContent": ["import type { DeepReadonly } from '../../shared/lib/deep-readonly'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  renderToReadableStream,\n  decodeReply,\n  decodeReplyFromAsyncIterable,\n  createTemporaryReferenceSet as createServerTemporaryReferenceSet,\n} from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  createFromReadableStream,\n  encodeReply,\n  createTemporaryReferenceSet as createClientTemporaryReferenceSet,\n} from 'react-server-dom-webpack/client.edge'\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport type {\n  UseCacheStore,\n  WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  getHmrRefreshHash,\n  getRenderResumeDataCache,\n  getPrerenderResumeDataCache,\n  workUnitAsyncStorage,\n} from '../app-render/work-unit-async-storage.external'\nimport { runInCleanSnapshot } from '../app-render/clean-async-snapshot.external'\n\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\n\nimport type { ClientReferenceManifestForRsc } from '../../build/webpack/plugins/flight-manifest-plugin'\n\nimport {\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n} from '../app-render/encryption-utils'\nimport type { CacheEntry } from '../lib/cache-handlers/types'\nimport type { CacheSignal } from '../app-render/cache-signal'\nimport { decryptActionBoundArgs } from '../app-render/encryption'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { getDigestForWellKnownError } from '../app-render/create-error-handler'\nimport { DYNAMIC_EXPIRE } from './constants'\nimport { getCacheHandler } from './handlers'\nimport { UseCacheTimeoutError } from './use-cache-errors'\nimport { createHangingInputAbortSignal } from '../app-render/dynamic-rendering'\nimport {\n  makeErroringExoticSearchParamsForUseCache,\n  type SearchParams,\n} from '../request/search-params'\nimport type { Params } from '../request/params'\nimport React from 'react'\n\ntype CacheKeyParts = [\n  buildId: string,\n  hmrRefreshHash: string | undefined,\n  id: string,\n  args: unknown[],\n]\n\nexport interface UseCachePageComponentProps {\n  params: Promise<Params>\n  searchParams: Promise<SearchParams>\n  $$isPageComponent: true\n}\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nfunction generateCacheEntry(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n  // generation cannot read anything from the context we're currently executing which\n  // might include request specific things like cookies() inside a React.cache().\n  // Note: It is important that we await at least once before this because it lets us\n  // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n  return runInCleanSnapshot(\n    generateCacheEntryWithRestoredWorkStore,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithRestoredWorkStore(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  // Since we cleared the AsyncLocalStorage we need to restore the workStore.\n  // Note: We explicitly don't restore the RequestStore nor the PrerenderStore.\n  // We don't want any request specific information leaking an we don't want to create a\n  // bloated fake request mock for every cache call. So any feature that currently lives\n  // in RequestStore but should be available to Caches need to move to WorkStore.\n  // PrerenderStore is not needed inside the cache scope because the outer most one will\n  // be the one to report its result to the outer Prerender.\n  return workAsyncStorage.run(\n    workStore,\n    generateCacheEntryWithCacheContext,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithCacheContext(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  if (!workStore.cacheLifeProfiles) {\n    throw new Error(\n      'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n    )\n  }\n  const defaultCacheLife = workStore.cacheLifeProfiles['default']\n  if (\n    !defaultCacheLife ||\n    defaultCacheLife.revalidate == null ||\n    defaultCacheLife.expire == null ||\n    defaultCacheLife.stale == null\n  ) {\n    throw new Error(\n      'A default cacheLife profile must always be provided. This is a bug in Next.js.'\n    )\n  }\n\n  const useCacheOrRequestStore =\n    outerWorkUnitStore?.type === 'request' ||\n    outerWorkUnitStore?.type === 'cache'\n      ? outerWorkUnitStore\n      : undefined\n\n  // Initialize the Store for this Cache entry.\n  const cacheStore: UseCacheStore = {\n    type: 'cache',\n    phase: 'render',\n    implicitTags:\n      outerWorkUnitStore === undefined ||\n      outerWorkUnitStore.type === 'unstable-cache'\n        ? []\n        : outerWorkUnitStore.implicitTags,\n    revalidate: defaultCacheLife.revalidate,\n    expire: defaultCacheLife.expire,\n    stale: defaultCacheLife.stale,\n    explicitRevalidate: undefined,\n    explicitExpire: undefined,\n    explicitStale: undefined,\n    tags: null,\n    hmrRefreshHash: outerWorkUnitStore && getHmrRefreshHash(outerWorkUnitStore),\n    isHmrRefresh: useCacheOrRequestStore?.isHmrRefresh ?? false,\n    serverComponentsHmrCache: useCacheOrRequestStore?.serverComponentsHmrCache,\n    forceRevalidate: shouldForceRevalidate(workStore, outerWorkUnitStore),\n  }\n\n  return workUnitAsyncStorage.run(\n    cacheStore,\n    generateCacheEntryImpl,\n    outerWorkUnitStore,\n    cacheStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction propagateCacheLifeAndTags(\n  workUnitStore: WorkUnitStore | undefined,\n  entry: CacheEntry\n): void {\n  if (\n    workUnitStore &&\n    (workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-ppr' ||\n      workUnitStore.type === 'prerender-legacy')\n  ) {\n    // Propagate tags and revalidate upwards\n    const outerTags = workUnitStore.tags ?? (workUnitStore.tags = [])\n    const entryTags = entry.tags\n    for (let i = 0; i < entryTags.length; i++) {\n      const tag = entryTags[i]\n      if (!outerTags.includes(tag)) {\n        outerTags.push(tag)\n      }\n    }\n    if (workUnitStore.stale > entry.stale) {\n      workUnitStore.stale = entry.stale\n    }\n    if (workUnitStore.revalidate > entry.revalidate) {\n      workUnitStore.revalidate = entry.revalidate\n    }\n    if (workUnitStore.expire > entry.expire) {\n      workUnitStore.expire = entry.expire\n    }\n  }\n}\n\nasync function collectResult(\n  savedStream: ReadableStream,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  startTime: number,\n  errors: Array<unknown>, // This is a live array that gets pushed into.,\n  timer: any\n): Promise<CacheEntry> {\n  // We create a buffered stream that collects all chunks until the end to\n  // ensure that RSC has finished rendering and therefore we have collected\n  // all tags. In the future the RSC API might allow for the equivalent of\n  // the allReady Promise that exists on SSR streams.\n  //\n  // If something errored or rejected anywhere in the render, we close\n  // the stream as errored. This lets a CacheHandler choose to save the\n  // partial result up until that point for future hits for a while to avoid\n  // unnecessary retries or not to retry. We use the end of the stream for\n  // this to avoid another complicated side-channel. A receiver has to consider\n  // that the stream might also error for other reasons anyway such as losing\n  // connection.\n\n  const buffer: any[] = []\n  const reader = savedStream.getReader()\n  for (let entry; !(entry = await reader.read()).done; ) {\n    buffer.push(entry.value)\n  }\n\n  let idx = 0\n  const bufferStream = new ReadableStream({\n    pull(controller) {\n      if (idx < buffer.length) {\n        controller.enqueue(buffer[idx++])\n      } else if (errors.length > 0) {\n        // TODO: Should we use AggregateError here?\n        controller.error(errors[0])\n      } else {\n        controller.close()\n      }\n    },\n  })\n\n  const collectedTags = innerCacheStore.tags\n  // If cacheLife() was used to set an explicit revalidate time we use that.\n  // Otherwise, we use the lowest of all inner fetch()/unstable_cache() or nested \"use cache\".\n  // If they're lower than our default.\n  const collectedRevalidate =\n    innerCacheStore.explicitRevalidate !== undefined\n      ? innerCacheStore.explicitRevalidate\n      : innerCacheStore.revalidate\n  const collectedExpire =\n    innerCacheStore.explicitExpire !== undefined\n      ? innerCacheStore.explicitExpire\n      : innerCacheStore.expire\n  const collectedStale =\n    innerCacheStore.explicitStale !== undefined\n      ? innerCacheStore.explicitStale\n      : innerCacheStore.stale\n\n  const entry = {\n    value: bufferStream,\n    timestamp: startTime,\n    revalidate: collectedRevalidate,\n    expire: collectedExpire,\n    stale: collectedStale,\n    tags: collectedTags === null ? [] : collectedTags,\n  }\n  // Propagate tags/revalidate to the parent context.\n  propagateCacheLifeAndTags(outerWorkUnitStore, entry)\n\n  const cacheSignal =\n    outerWorkUnitStore && outerWorkUnitStore.type === 'prerender'\n      ? outerWorkUnitStore.cacheSignal\n      : null\n  if (cacheSignal) {\n    cacheSignal.endRead()\n  }\n\n  if (timer !== undefined) {\n    clearTimeout(timer)\n  }\n\n  return entry\n}\n\nasync function generateCacheEntryImpl(\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  const temporaryReferences = createServerTemporaryReferenceSet()\n\n  const [, , , args] =\n    typeof encodedArguments === 'string'\n      ? await decodeReply<CacheKeyParts>(\n          encodedArguments,\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n      : await decodeReplyFromAsyncIterable<CacheKeyParts>(\n          {\n            async *[Symbol.asyncIterator]() {\n              for (const entry of encodedArguments) {\n                yield entry\n              }\n\n              // The encoded arguments might contain hanging promises. In this\n              // case we don't want to reject with \"Error: Connection closed.\",\n              // so we intentionally keep the iterable alive. This is similar to\n              // the halting trick that we do while rendering.\n              if (outerWorkUnitStore?.type === 'prerender') {\n                await new Promise<void>((resolve) => {\n                  if (outerWorkUnitStore.renderSignal.aborted) {\n                    resolve()\n                  } else {\n                    outerWorkUnitStore.renderSignal.addEventListener(\n                      'abort',\n                      () => resolve(),\n                      { once: true }\n                    )\n                  }\n                })\n              }\n            },\n          },\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n\n  // Track the timestamp when we started computing the result.\n  const startTime = performance.timeOrigin + performance.now()\n\n  // Invoke the inner function to load a new result. We delay the invocation\n  // though, until React awaits the promise so that React's request store (ALS)\n  // is available when the function is invoked. This allows us, for example, to\n  // capture logs so that we can later replay them.\n  const resultPromise = createLazyResult(() => fn.apply(null, args))\n\n  let errors: Array<unknown> = []\n\n  let timer = undefined\n  const controller = new AbortController()\n  if (outerWorkUnitStore?.type === 'prerender') {\n    // If we're prerendering, we give you 50 seconds to fill a cache entry.\n    // Otherwise we assume you stalled on hanging input and de-opt. This needs\n    // to be lower than just the general timeout of 60 seconds.\n    timer = setTimeout(() => {\n      controller.abort(timeoutError)\n    }, 50000)\n  }\n\n  const stream = renderToReadableStream(\n    resultPromise,\n    clientReferenceManifest.clientModules,\n    {\n      environmentName: 'Cache',\n      signal: controller.signal,\n      temporaryReferences,\n      // In the \"Cache\" environment, we only need to make sure that the error\n      // digests are handled correctly. Error formatting and reporting is not\n      // necessary here; the errors are encoded in the stream, and will be\n      // reported in the \"Server\" environment.\n      onError: (error) => {\n        const digest = getDigestForWellKnownError(error)\n\n        if (digest) {\n          return digest\n        }\n\n        if (process.env.NODE_ENV !== 'development') {\n          // TODO: For now we're also reporting the error here, because in\n          // production, the \"Server\" environment will only get the obfuscated\n          // error (created by the Flight Client in the cache wrapper).\n          console.error(error)\n        }\n\n        if (error === timeoutError) {\n          // The timeout error already aborted the whole stream. We don't need\n          // to also push this error into the `errors` array.\n          return timeoutError.digest\n        }\n\n        errors.push(error)\n      },\n    }\n  )\n\n  const [returnStream, savedStream] = stream.tee()\n\n  const promiseOfCacheEntry = collectResult(\n    savedStream,\n    outerWorkUnitStore,\n    innerCacheStore,\n    startTime,\n    errors,\n    timer\n  )\n\n  // Return the stream as we're creating it. This means that if it ends up\n  // erroring we cannot return a stale-while-error version but it allows\n  // streaming back the result earlier.\n  return [returnStream, promiseOfCacheEntry]\n}\n\nfunction cloneCacheEntry(entry: CacheEntry): [CacheEntry, CacheEntry] {\n  const [streamA, streamB] = entry.value.tee()\n  entry.value = streamA\n  const clonedEntry: CacheEntry = {\n    value: streamB,\n    timestamp: entry.timestamp,\n    revalidate: entry.revalidate,\n    expire: entry.expire,\n    stale: entry.stale,\n    tags: entry.tags,\n  }\n  return [entry, clonedEntry]\n}\n\nasync function clonePendingCacheEntry(\n  pendingCacheEntry: Promise<CacheEntry>\n): Promise<[CacheEntry, CacheEntry]> {\n  const entry = await pendingCacheEntry\n  return cloneCacheEntry(entry)\n}\n\nasync function getNthCacheEntry(\n  split: Promise<[CacheEntry, CacheEntry]>,\n  i: number\n): Promise<CacheEntry> {\n  return (await split)[i]\n}\n\nasync function encodeFormData(formData: FormData): Promise<string> {\n  let result = ''\n  for (let [key, value] of formData) {\n    // We don't need this key to be serializable but from a security perspective it should not be\n    // possible to generate a string that looks the same from a different structure. To ensure this\n    // we need a delimeter between fields but just using a delimeter is not enough since a string\n    // might contain that delimeter. We use the length of each field as the delimeter to avoid\n    // escaping the values.\n    result += key.length.toString(16) + ':' + key\n    let stringValue\n    if (typeof value === 'string') {\n      stringValue = value\n    } else {\n      // The FormData might contain binary data that is not valid UTF-8 so this cache\n      // key may generate a UCS-2 string. Passing this to another service needs to be\n      // aware that the key might not be compatible.\n      const arrayBuffer = await value.arrayBuffer()\n      if (arrayBuffer.byteLength % 2 === 0) {\n        stringValue = String.fromCodePoint(...new Uint16Array(arrayBuffer))\n      } else {\n        stringValue =\n          String.fromCodePoint(\n            ...new Uint16Array(arrayBuffer, 0, (arrayBuffer.byteLength - 1) / 2)\n          ) +\n          String.fromCodePoint(\n            new Uint8Array(arrayBuffer, arrayBuffer.byteLength - 1, 1)[0]\n          )\n      }\n    }\n    result += stringValue.length.toString(16) + ':' + stringValue\n  }\n  return result\n}\n\nfunction createTrackedReadableStream(\n  stream: ReadableStream,\n  cacheSignal: CacheSignal\n) {\n  const reader = stream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read()\n      if (done) {\n        controller.close()\n        cacheSignal.endRead()\n      } else {\n        controller.enqueue(value)\n      }\n    },\n  })\n}\n\nexport function cache(\n  kind: string,\n  id: string,\n  boundArgsLength: number,\n  fn: (...args: unknown[]) => Promise<unknown>\n) {\n  const cacheHandler = getCacheHandler(kind)\n  if (cacheHandler === undefined) {\n    throw new Error('Unknown cache handler: ' + kind)\n  }\n\n  // Capture the timeout error here to ensure a useful stack.\n  const timeoutError = new UseCacheTimeoutError()\n  Error.captureStackTrace(timeoutError, cache)\n\n  const name = fn.name\n  const cachedFn = {\n    [name]: async function (...args: any[]) {\n      const workStore = workAsyncStorage.getStore()\n      if (workStore === undefined) {\n        throw new Error(\n          '\"use cache\" cannot be used outside of App Router. Expected a WorkStore.'\n        )\n      }\n\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      // Get the clientReferenceManifest while we're still in the outer Context.\n      // In case getClientReferenceManifestSingleton is implemented using AsyncLocalStorage.\n      const clientReferenceManifest = getClientReferenceManifestForRsc()\n\n      // Because the Action ID is not yet unique per implementation of that Action we can't\n      // safely reuse the results across builds yet. In the meantime we add the buildId to the\n      // arguments as a seed to ensure they're not reused. Remove this once Action IDs hash\n      // the implementation.\n      const buildId = workStore.buildId\n\n      // In dev mode, when the HMR refresh hash is set, we include it in the\n      // cache key. This ensures that cache entries are not reused when server\n      // components have been edited. This is a very coarse approach. But it's\n      // also only a temporary solution until Action IDs are unique per\n      // implementation. Remove this once Action IDs hash the implementation.\n      const hmrRefreshHash = workUnitStore && getHmrRefreshHash(workUnitStore)\n\n      const hangingInputAbortSignal =\n        workUnitStore?.type === 'prerender'\n          ? createHangingInputAbortSignal(workUnitStore)\n          : undefined\n\n      // When dynamicIO is not enabled, we can not encode searchParams as\n      // hanging promises. To still avoid unused search params from making a\n      // page dynamic, we overwrite them here with a promise that resolves to an\n      // empty object, while also overwriting the to-be-invoked function for\n      // generating a cache entry with a function that creates an erroring\n      // searchParams prop before invoking the original function. This ensures\n      // that used searchParams inside of cached functions would still yield an\n      // error.\n      if (!workStore.dynamicIOEnabled && isPageComponent(args)) {\n        const [{ params, searchParams }] = args\n        // Overwrite the props to omit $$isPageComponent.\n        args = [{ params, searchParams }]\n\n        const originalFn = fn\n\n        fn = {\n          [name]: async ({\n            params: serializedParams,\n          }: Omit<UseCachePageComponentProps, '$$isPageComponent'>) =>\n            originalFn.apply(null, [\n              {\n                params: serializedParams,\n                searchParams:\n                  makeErroringExoticSearchParamsForUseCache(workStore),\n              },\n            ]),\n        }[name] as (...args: unknown[]) => Promise<unknown>\n      }\n\n      if (boundArgsLength > 0) {\n        if (args.length === 0) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive its encrypted bound arguments as the first argument.`\n          )\n        }\n\n        const encryptedBoundArgs = args.shift()\n        const boundArgs = await decryptActionBoundArgs(id, encryptedBoundArgs)\n\n        if (!Array.isArray(boundArgs)) {\n          throw new InvariantError(\n            `Expected the bound arguments of \"use cache\" function ${JSON.stringify(fn.name)} to deserialize into an array, got ${typeof boundArgs} instead.`\n          )\n        }\n\n        if (boundArgsLength !== boundArgs.length) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive ${boundArgsLength} bound arguments, got ${boundArgs.length} instead.`\n          )\n        }\n\n        args.unshift(boundArgs)\n      }\n\n      const temporaryReferences = createClientTemporaryReferenceSet()\n      const cacheKeyParts: CacheKeyParts = [buildId, hmrRefreshHash, id, args]\n      const encodedCacheKeyParts: FormData | string = await encodeReply(\n        cacheKeyParts,\n        { temporaryReferences, signal: hangingInputAbortSignal }\n      )\n\n      const serializedCacheKey =\n        typeof encodedCacheKeyParts === 'string'\n          ? // Fast path for the simple case for simple inputs. We let the CacheHandler\n            // Convert it to an ArrayBuffer if it wants to.\n            encodedCacheKeyParts\n          : await encodeFormData(encodedCacheKeyParts)\n\n      let stream: undefined | ReadableStream = undefined\n\n      // Get an immutable and mutable versions of the resume data cache.\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      const renderResumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n\n      if (renderResumeDataCache) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n\n        if (cacheSignal) {\n          cacheSignal.beginRead()\n        }\n        const cachedEntry = renderResumeDataCache.cache.get(serializedCacheKey)\n        if (cachedEntry !== undefined) {\n          const existingEntry = await cachedEntry\n          propagateCacheLifeAndTags(workUnitStore, existingEntry)\n          if (\n            workUnitStore !== undefined &&\n            workUnitStore.type === 'prerender' &&\n            existingEntry !== undefined &&\n            (existingEntry.revalidate === 0 ||\n              existingEntry.expire < DYNAMIC_EXPIRE)\n          ) {\n            // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n            // expire time is under 5 minutes, then we consider this cache entry dynamic\n            // as it's not worth generating static pages for such data. It's better to leave\n            // a PPR hole that can be filled in dynamically with a potentially cached entry.\n            if (cacheSignal) {\n              cacheSignal.endRead()\n            }\n            return makeHangingPromise(\n              workUnitStore.renderSignal,\n              'dynamic \"use cache\"'\n            )\n          }\n          const [streamA, streamB] = existingEntry.value.tee()\n          existingEntry.value = streamB\n\n          if (cacheSignal) {\n            // When we have a cacheSignal we need to block on reading the cache\n            // entry before ending the read.\n            stream = createTrackedReadableStream(streamA, cacheSignal)\n          } else {\n            stream = streamA\n          }\n        } else {\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n        }\n      }\n\n      if (stream === undefined) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n        if (cacheSignal) {\n          // Either the cache handler or the generation can be using I/O at this point.\n          // We need to track when they start and when they complete.\n          cacheSignal.beginRead()\n        }\n\n        const implicitTags =\n          workUnitStore === undefined || workUnitStore.type === 'unstable-cache'\n            ? []\n            : workUnitStore.implicitTags\n\n        const forceRevalidate = shouldForceRevalidate(workStore, workUnitStore)\n\n        const entry = forceRevalidate\n          ? undefined\n          : await cacheHandler.get(serializedCacheKey, implicitTags)\n\n        const currentTime = performance.timeOrigin + performance.now()\n        if (\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender' &&\n          entry !== undefined &&\n          (entry.revalidate === 0 || entry.expire < DYNAMIC_EXPIRE)\n        ) {\n          // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n          // expire time is under 5 minutes, then we consider this cache entry dynamic\n          // as it's not worth generating static pages for such data. It's better to leave\n          // a PPR hole that can be filled in dynamically with a potentially cached entry.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            'dynamic \"use cache\"'\n          )\n        } else if (\n          entry === undefined ||\n          currentTime > entry.timestamp + entry.expire * 1000 ||\n          (workStore.isStaticGeneration &&\n            currentTime > entry.timestamp + entry.revalidate * 1000)\n        ) {\n          // Miss. Generate a new result.\n\n          // If the cache entry is stale and we're prerendering, we don't want to use the\n          // stale entry since it would unnecessarily need to shorten the lifetime of the\n          // prerender. We're not time constrained here so we can re-generated it now.\n\n          // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n          // generation cannot read anything from the context we're currently executing which\n          // might include request specific things like cookies() inside a React.cache().\n          // Note: It is important that we await at least once before this because it lets us\n          // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n\n          const [newStream, pendingCacheEntry] = await generateCacheEntry(\n            workStore,\n            workUnitStore,\n            clientReferenceManifest,\n            encodedCacheKeyParts,\n            fn,\n            timeoutError\n          )\n\n          let savedCacheEntry\n          if (prerenderResumeDataCache) {\n            // Create a clone that goes into the cache scope memory cache.\n            const split = clonePendingCacheEntry(pendingCacheEntry)\n            savedCacheEntry = getNthCacheEntry(split, 0)\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              getNthCacheEntry(split, 1)\n            )\n          } else {\n            savedCacheEntry = pendingCacheEntry\n          }\n\n          const promise = cacheHandler.set(serializedCacheKey, savedCacheEntry)\n\n          if (!workStore.pendingRevalidateWrites) {\n            workStore.pendingRevalidateWrites = []\n          }\n          workStore.pendingRevalidateWrites.push(promise)\n\n          stream = newStream\n        } else {\n          propagateCacheLifeAndTags(workUnitStore, entry)\n\n          // We want to return this stream, even if it's stale.\n          stream = entry.value\n\n          // If we have a cache scope, we need to clone the entry and set it on\n          // the inner cache scope.\n          if (prerenderResumeDataCache) {\n            const [entryLeft, entryRight] = cloneCacheEntry(entry)\n            if (cacheSignal) {\n              stream = createTrackedReadableStream(entryLeft.value, cacheSignal)\n            } else {\n              stream = entryLeft.value\n            }\n\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              Promise.resolve(entryRight)\n            )\n          } else {\n            // If we're not regenerating we need to signal that we've finished\n            // putting the entry into the cache scope at this point. Otherwise we do\n            // that inside generateCacheEntry.\n            cacheSignal?.endRead()\n          }\n\n          if (currentTime > entry.timestamp + entry.revalidate * 1000) {\n            // If this is stale, and we're not in a prerender (i.e. this is dynamic render),\n            // then we should warm up the cache with a fresh revalidated entry.\n            const [ignoredStream, pendingCacheEntry] = await generateCacheEntry(\n              workStore,\n              undefined, // This is not running within the context of this unit.\n              clientReferenceManifest,\n              encodedCacheKeyParts,\n              fn,\n              timeoutError\n            )\n\n            let savedCacheEntry: Promise<CacheEntry>\n            if (prerenderResumeDataCache) {\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            const promise = cacheHandler.set(\n              serializedCacheKey,\n              savedCacheEntry\n            )\n\n            if (!workStore.pendingRevalidateWrites) {\n              workStore.pendingRevalidateWrites = []\n            }\n            workStore.pendingRevalidateWrites.push(promise)\n\n            await ignoredStream.cancel()\n          }\n        }\n      }\n\n      // Logs are replayed even if it's a hit - to ensure we see them on the client eventually.\n      // If we didn't then the client wouldn't see the logs if it was seeded from a prewarm that\n      // never made it to the client. However, this also means that you see logs even when the\n      // cached function isn't actually re-executed. We should instead ensure prewarms always\n      // make it to the client. Another issue is that this will cause double logging in the\n      // server terminal. Once while generating the cache entry and once when replaying it on\n      // the server, which is required to pick it up for replaying again on the client.\n      const replayConsoleLogs = true\n\n      const serverConsumerManifest = {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n        // which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime\n          ? clientReferenceManifest.edgeRscModuleMapping\n          : clientReferenceManifest.rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      }\n\n      return createFromReadableStream(stream, {\n        serverConsumerManifest,\n        temporaryReferences,\n        replayConsoleLogs,\n        environmentName: 'Cache',\n      })\n    },\n  }[name]\n\n  return React.cache(cachedFn)\n}\n\n/**\n * Calls the given function only when the returned promise is awaited.\n */\nfunction createLazyResult<TResult>(\n  fn: () => Promise<TResult>\n): PromiseLike<TResult> {\n  let pendingResult: Promise<TResult> | undefined\n\n  return {\n    then(onfulfilled, onrejected) {\n      if (!pendingResult) {\n        pendingResult = fn()\n      }\n\n      return pendingResult.then(onfulfilled, onrejected)\n    },\n  }\n}\n\nfunction isPageComponent(\n  args: any[]\n): args is [UseCachePageComponentProps, undefined] {\n  if (args.length !== 2) {\n    return false\n  }\n\n  const [props, ref] = args\n\n  return (\n    ref === undefined && // server components receive an undefined ref arg\n    props !== null &&\n    typeof props === 'object' &&\n    (props as UseCachePageComponentProps).$$isPageComponent\n  )\n}\n\nfunction shouldForceRevalidate(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore | undefined\n): boolean {\n  if (workStore.isOnDemandRevalidate) {\n    return true\n  }\n\n  if (workStore.dev && workUnitStore) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.headers.get('cache-control') === 'no-cache'\n    }\n\n    if (workUnitStore.type === 'cache') {\n      return workUnitStore.forceRevalidate\n    }\n  }\n\n  return false\n}\n"], "names": ["renderToReadableStream", "decodeReply", "decodeReplyFromAsyncIterable", "createTemporaryReferenceSet", "createServerTemporaryReferenceSet", "createFromReadableStream", "encodeReply", "createClientTemporaryReferenceSet", "workAsyncStorage", "getHmrRefreshHash", "getRenderResumeDataCache", "getPrerenderResumeDataCache", "workUnitAsyncStorage", "runInCleanSnapshot", "makeHangingPromise", "getClientReferenceManifestForRsc", "getServerModuleMap", "decryptActionBoundArgs", "InvariantError", "getDigestForWellKnownError", "DYNAMIC_EXPIRE", "getCache<PERSON><PERSON><PERSON>", "UseCacheTimeoutError", "createHangingInputAbortSignal", "makeErroringExoticSearchParamsForUseCache", "React", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "generateCacheEntry", "workStore", "outerWorkUnitStore", "clientReferenceManifest", "encodedArguments", "fn", "timeoutError", "generateCacheEntryWithRestoredWorkStore", "run", "generateCacheEntryWithCacheContext", "cacheLifeProfiles", "Error", "defaultCacheLife", "revalidate", "expire", "stale", "useCacheOrRequestStore", "type", "undefined", "cacheStore", "phase", "implicitTags", "explicitRevalidate", "explicitExpire", "explicitStale", "tags", "hmrRefreshHash", "isHmrRefresh", "serverComponentsHmrCache", "forceRevalidate", "shouldForceRevalidate", "generateCacheEntryImpl", "propagateCacheLifeAndTags", "workUnitStore", "entry", "outerTags", "entryTags", "i", "length", "tag", "includes", "push", "collectResult", "savedStream", "innerCacheStore", "startTime", "errors", "timer", "buffer", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "value", "idx", "bufferStream", "ReadableStream", "pull", "controller", "enqueue", "error", "close", "collectedTags", "collectedRevalidate", "collectedExpire", "collectedStale", "timestamp", "cacheSignal", "endRead", "clearTimeout", "temporaryReferences", "args", "Symbol", "asyncIterator", "Promise", "resolve", "renderSignal", "aborted", "addEventListener", "once", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "resultPromise", "createLazyResult", "apply", "AbortController", "setTimeout", "abort", "stream", "clientModules", "environmentName", "signal", "onError", "digest", "NODE_ENV", "console", "returnStream", "tee", "promiseOfCacheEntry", "cloneCacheEntry", "streamA", "streamB", "clonedEntry", "clonePendingCacheEntry", "pendingCacheEntry", "getNthCacheEntry", "split", "encodeFormData", "formData", "result", "key", "toString", "stringValue", "arrayBuffer", "byteLength", "String", "fromCodePoint", "Uint16Array", "Uint8Array", "createTrackedReadableStream", "cache", "kind", "id", "boundArgs<PERSON><PERSON>th", "cache<PERSON><PERSON><PERSON>", "captureStackTrace", "name", "cachedFn", "getStore", "buildId", "hangingInputAbortSignal", "dynamicIOEnabled", "isPageComponent", "params", "searchParams", "originalFn", "serializedParams", "JSON", "stringify", "encryptedBoundArgs", "shift", "boundArgs", "Array", "isArray", "unshift", "cacheKeyParts", "encodedCacheKeyParts", "serialized<PERSON>ache<PERSON>ey", "prerenderResumeDataCache", "renderResumeDataCache", "beginRead", "cachedEntry", "get", "existingEntry", "currentTime", "isStaticGeneration", "newStream", "savedCacheEntry", "set", "promise", "pendingRevalidateWrites", "entryLeft", "entryRight", "ignoredStream", "cancel", "replayConsoleLogs", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "serverModuleMap", "pendingResult", "then", "onfulfilled", "onrejected", "props", "ref", "$$isPageComponent", "isOnDemandRevalidate", "dev", "headers"], "mappings": "AACA,oDAAoD,GACpD,SACEA,sBAAsB,EACtBC,WAAW,EACXC,4BAA4B,EAC5BC,+BAA+BC,iCAAiC,QAC3D,uCAAsC;AAC7C,oDAAoD,GACpD,SACEC,wBAAwB,EACxBC,WAAW,EACXH,+BAA+BI,iCAAiC,QAC3D,uCAAsC;AAG7C,SAASC,gBAAgB,QAAQ,4CAA2C;AAK5E,SACEC,iBAAiB,EACjBC,wBAAwB,EACxBC,2BAA2B,EAC3BC,oBAAoB,QACf,iDAAgD;AACvD,SAASC,kBAAkB,QAAQ,8CAA6C;AAEhF,SAASC,kBAAkB,QAAQ,6BAA4B;AAI/D,SACEC,gCAAgC,EAChCC,kBAAkB,QACb,iCAAgC;AAGvC,SAASC,sBAAsB,QAAQ,2BAA0B;AACjE,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,0BAA0B,QAAQ,qCAAoC;AAC/E,SAASC,cAAc,QAAQ,cAAa;AAC5C,SAASC,eAAe,QAAQ,aAAY;AAC5C,SAASC,oBAAoB,QAAQ,qBAAoB;AACzD,SAASC,6BAA6B,QAAQ,kCAAiC;AAC/E,SACEC,yCAAyC,QAEpC,2BAA0B;AAEjC,OAAOC,WAAW,QAAO;AAezB,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,SAASC,mBACPC,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,kFAAkF;IAClF,mFAAmF;IACnF,+EAA+E;IAC/E,mFAAmF;IACnF,6EAA6E;IAC7E,OAAOvB,mBACLwB,yCACAN,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASC,wCACPN,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,2EAA2E;IAC3E,6EAA6E;IAC7E,sFAAsF;IACtF,sFAAsF;IACtF,+EAA+E;IAC/E,sFAAsF;IACtF,0DAA0D;IAC1D,OAAO5B,iBAAiB8B,GAAG,CACzBP,WACAQ,oCACAR,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASG,mCACPR,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,IAAI,CAACL,UAAUS,iBAAiB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,2EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,MAAMC,mBAAmBX,UAAUS,iBAAiB,CAAC,UAAU;IAC/D,IACE,CAACE,oBACDA,iBAAiBC,UAAU,IAAI,QAC/BD,iBAAiBE,MAAM,IAAI,QAC3BF,iBAAiBG,KAAK,IAAI,MAC1B;QACA,MAAM,qBAEL,CAFK,IAAIJ,MACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMK,yBACJd,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,aAC7Bf,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,UACzBf,qBACAgB;IAEN,6CAA6C;IAC7C,MAAMC,aAA4B;QAChCF,MAAM;QACNG,OAAO;QACPC,cACEnB,uBAAuBgB,aACvBhB,mBAAmBe,IAAI,KAAK,mBACxB,EAAE,GACFf,mBAAmBmB,YAAY;QACrCR,YAAYD,iBAAiBC,UAAU;QACvCC,QAAQF,iBAAiBE,MAAM;QAC/BC,OAAOH,iBAAiBG,KAAK;QAC7BO,oBAAoBJ;QACpBK,gBAAgBL;QAChBM,eAAeN;QACfO,MAAM;QACNC,gBAAgBxB,sBAAsBvB,kBAAkBuB;QACxDyB,cAAcX,CAAAA,0CAAAA,uBAAwBW,YAAY,KAAI;QACtDC,wBAAwB,EAAEZ,0CAAAA,uBAAwBY,wBAAwB;QAC1EC,iBAAiBC,sBAAsB7B,WAAWC;IACpD;IAEA,OAAOpB,qBAAqB0B,GAAG,CAC7BW,YACAY,wBACA7B,oBACAiB,YACAhB,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAAS0B,0BACPC,aAAwC,EACxCC,KAAiB;IAEjB,IACED,iBACCA,CAAAA,cAAchB,IAAI,KAAK,WACtBgB,cAAchB,IAAI,KAAK,eACvBgB,cAAchB,IAAI,KAAK,mBACvBgB,cAAchB,IAAI,KAAK,kBAAiB,GAC1C;QACA,wCAAwC;QACxC,MAAMkB,YAAYF,cAAcR,IAAI,IAAKQ,CAAAA,cAAcR,IAAI,GAAG,EAAE,AAAD;QAC/D,MAAMW,YAAYF,MAAMT,IAAI;QAC5B,IAAK,IAAIY,IAAI,GAAGA,IAAID,UAAUE,MAAM,EAAED,IAAK;YACzC,MAAME,MAAMH,SAAS,CAACC,EAAE;YACxB,IAAI,CAACF,UAAUK,QAAQ,CAACD,MAAM;gBAC5BJ,UAAUM,IAAI,CAACF;YACjB;QACF;QACA,IAAIN,cAAclB,KAAK,GAAGmB,MAAMnB,KAAK,EAAE;YACrCkB,cAAclB,KAAK,GAAGmB,MAAMnB,KAAK;QACnC;QACA,IAAIkB,cAAcpB,UAAU,GAAGqB,MAAMrB,UAAU,EAAE;YAC/CoB,cAAcpB,UAAU,GAAGqB,MAAMrB,UAAU;QAC7C;QACA,IAAIoB,cAAcnB,MAAM,GAAGoB,MAAMpB,MAAM,EAAE;YACvCmB,cAAcnB,MAAM,GAAGoB,MAAMpB,MAAM;QACrC;IACF;AACF;AAEA,eAAe4B,cACbC,WAA2B,EAC3BzC,kBAA6C,EAC7C0C,eAA8B,EAC9BC,SAAiB,EACjBC,MAAsB,EACtBC,KAAU;IAEV,wEAAwE;IACxE,yEAAyE;IACzE,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,oEAAoE;IACpE,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,2EAA2E;IAC3E,cAAc;IAEd,MAAMC,SAAgB,EAAE;IACxB,MAAMC,SAASN,YAAYO,SAAS;IACpC,IAAK,IAAIhB,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMe,OAAOE,IAAI,EAAC,EAAGC,IAAI,EAAI;QACrDJ,OAAOP,IAAI,CAACP,MAAMmB,KAAK;IACzB;IAEA,IAAIC,MAAM;IACV,MAAMC,eAAe,IAAIC,eAAe;QACtCC,MAAKC,UAAU;YACb,IAAIJ,MAAMN,OAAOV,MAAM,EAAE;gBACvBoB,WAAWC,OAAO,CAACX,MAAM,CAACM,MAAM;YAClC,OAAO,IAAIR,OAAOR,MAAM,GAAG,GAAG;gBAC5B,2CAA2C;gBAC3CoB,WAAWE,KAAK,CAACd,MAAM,CAAC,EAAE;YAC5B,OAAO;gBACLY,WAAWG,KAAK;YAClB;QACF;IACF;IAEA,MAAMC,gBAAgBlB,gBAAgBnB,IAAI;IAC1C,0EAA0E;IAC1E,4FAA4F;IAC5F,qCAAqC;IACrC,MAAMsC,sBACJnB,gBAAgBtB,kBAAkB,KAAKJ,YACnC0B,gBAAgBtB,kBAAkB,GAClCsB,gBAAgB/B,UAAU;IAChC,MAAMmD,kBACJpB,gBAAgBrB,cAAc,KAAKL,YAC/B0B,gBAAgBrB,cAAc,GAC9BqB,gBAAgB9B,MAAM;IAC5B,MAAMmD,iBACJrB,gBAAgBpB,aAAa,KAAKN,YAC9B0B,gBAAgBpB,aAAa,GAC7BoB,gBAAgB7B,KAAK;IAE3B,MAAMmB,QAAQ;QACZmB,OAAOE;QACPW,WAAWrB;QACXhC,YAAYkD;QACZjD,QAAQkD;QACRjD,OAAOkD;QACPxC,MAAMqC,kBAAkB,OAAO,EAAE,GAAGA;IACtC;IACA,mDAAmD;IACnD9B,0BAA0B9B,oBAAoBgC;IAE9C,MAAMiC,cACJjE,sBAAsBA,mBAAmBe,IAAI,KAAK,cAC9Cf,mBAAmBiE,WAAW,GAC9B;IACN,IAAIA,aAAa;QACfA,YAAYC,OAAO;IACrB;IAEA,IAAIrB,UAAU7B,WAAW;QACvBmD,aAAatB;IACf;IAEA,OAAOb;AACT;AAEA,eAAeH,uBACb7B,kBAA6C,EAC7C0C,eAA8B,EAC9BzC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,MAAMgE,sBAAsBhG;IAE5B,MAAM,OAAOiG,KAAK,GAChB,OAAOnE,qBAAqB,WACxB,MAAMjC,YACJiC,kBACAlB,sBACA;QAAEoF;IAAoB,KAExB,MAAMlG,6BACJ;QACE,OAAO,CAACoG,OAAOC,aAAa,CAAC;YAC3B,KAAK,MAAMvC,SAAS9B,iBAAkB;gBACpC,MAAM8B;YACR;YAEA,gEAAgE;YAChE,iEAAiE;YACjE,kEAAkE;YAClE,gDAAgD;YAChD,IAAIhC,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,aAAa;gBAC5C,MAAM,IAAIyD,QAAc,CAACC;oBACvB,IAAIzE,mBAAmB0E,YAAY,CAACC,OAAO,EAAE;wBAC3CF;oBACF,OAAO;wBACLzE,mBAAmB0E,YAAY,CAACE,gBAAgB,CAC9C,SACA,IAAMH,WACN;4BAAEI,MAAM;wBAAK;oBAEjB;gBACF;YACF;QACF;IACF,GACA7F,sBACA;QAAEoF;IAAoB;IAG9B,4DAA4D;IAC5D,MAAMzB,YAAYmC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;IAE1D,0EAA0E;IAC1E,6EAA6E;IAC7E,6EAA6E;IAC7E,iDAAiD;IACjD,MAAMC,gBAAgBC,iBAAiB,IAAM/E,GAAGgF,KAAK,CAAC,MAAMd;IAE5D,IAAIzB,SAAyB,EAAE;IAE/B,IAAIC,QAAQ7B;IACZ,MAAMwC,aAAa,IAAI4B;IACvB,IAAIpF,CAAAA,sCAAAA,mBAAoBe,IAAI,MAAK,aAAa;QAC5C,uEAAuE;QACvE,0EAA0E;QAC1E,2DAA2D;QAC3D8B,QAAQwC,WAAW;YACjB7B,WAAW8B,KAAK,CAAClF;QACnB,GAAG;IACL;IAEA,MAAMmF,SAASvH,uBACbiH,eACAhF,wBAAwBuF,aAAa,EACrC;QACEC,iBAAiB;QACjBC,QAAQlC,WAAWkC,MAAM;QACzBtB;QACA,uEAAuE;QACvE,uEAAuE;QACvE,oEAAoE;QACpE,wCAAwC;QACxCuB,SAAS,CAACjC;YACR,MAAMkC,SAASzG,2BAA2BuE;YAE1C,IAAIkC,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAIjG,QAAQC,GAAG,CAACiG,QAAQ,KAAK,eAAe;gBAC1C,gEAAgE;gBAChE,oEAAoE;gBACpE,6DAA6D;gBAC7DC,QAAQpC,KAAK,CAACA;YAChB;YAEA,IAAIA,UAAUtD,cAAc;gBAC1B,oEAAoE;gBACpE,mDAAmD;gBACnD,OAAOA,aAAawF,MAAM;YAC5B;YAEAhD,OAAOL,IAAI,CAACmB;QACd;IACF;IAGF,MAAM,CAACqC,cAActD,YAAY,GAAG8C,OAAOS,GAAG;IAE9C,MAAMC,sBAAsBzD,cAC1BC,aACAzC,oBACA0C,iBACAC,WACAC,QACAC;IAGF,wEAAwE;IACxE,sEAAsE;IACtE,qCAAqC;IACrC,OAAO;QAACkD;QAAcE;KAAoB;AAC5C;AAEA,SAASC,gBAAgBlE,KAAiB;IACxC,MAAM,CAACmE,SAASC,QAAQ,GAAGpE,MAAMmB,KAAK,CAAC6C,GAAG;IAC1ChE,MAAMmB,KAAK,GAAGgD;IACd,MAAME,cAA0B;QAC9BlD,OAAOiD;QACPpC,WAAWhC,MAAMgC,SAAS;QAC1BrD,YAAYqB,MAAMrB,UAAU;QAC5BC,QAAQoB,MAAMpB,MAAM;QACpBC,OAAOmB,MAAMnB,KAAK;QAClBU,MAAMS,MAAMT,IAAI;IAClB;IACA,OAAO;QAACS;QAAOqE;KAAY;AAC7B;AAEA,eAAeC,uBACbC,iBAAsC;IAEtC,MAAMvE,QAAQ,MAAMuE;IACpB,OAAOL,gBAAgBlE;AACzB;AAEA,eAAewE,iBACbC,KAAwC,EACxCtE,CAAS;IAET,OAAO,AAAC,CAAA,MAAMsE,KAAI,CAAE,CAACtE,EAAE;AACzB;AAEA,eAAeuE,eAAeC,QAAkB;IAC9C,IAAIC,SAAS;IACb,KAAK,IAAI,CAACC,KAAK1D,MAAM,IAAIwD,SAAU;QACjC,6FAA6F;QAC7F,+FAA+F;QAC/F,6FAA6F;QAC7F,0FAA0F;QAC1F,uBAAuB;QACvBC,UAAUC,IAAIzE,MAAM,CAAC0E,QAAQ,CAAC,MAAM,MAAMD;QAC1C,IAAIE;QACJ,IAAI,OAAO5D,UAAU,UAAU;YAC7B4D,cAAc5D;QAChB,OAAO;YACL,+EAA+E;YAC/E,+EAA+E;YAC/E,8CAA8C;YAC9C,MAAM6D,cAAc,MAAM7D,MAAM6D,WAAW;YAC3C,IAAIA,YAAYC,UAAU,GAAG,MAAM,GAAG;gBACpCF,cAAcG,OAAOC,aAAa,IAAI,IAAIC,YAAYJ;YACxD,OAAO;gBACLD,cACEG,OAAOC,aAAa,IACf,IAAIC,YAAYJ,aAAa,GAAG,AAACA,CAAAA,YAAYC,UAAU,GAAG,CAAA,IAAK,MAEpEC,OAAOC,aAAa,CAClB,IAAIE,WAAWL,aAAaA,YAAYC,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE;YAEnE;QACF;QACAL,UAAUG,YAAY3E,MAAM,CAAC0E,QAAQ,CAAC,MAAM,MAAMC;IACpD;IACA,OAAOH;AACT;AAEA,SAASU,4BACP/B,MAAsB,EACtBtB,WAAwB;IAExB,MAAMlB,SAASwC,OAAOvC,SAAS;IAC/B,OAAO,IAAIM,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAM,EAAEN,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOE,IAAI;YACzC,IAAIC,MAAM;gBACRM,WAAWG,KAAK;gBAChBM,YAAYC,OAAO;YACrB,OAAO;gBACLV,WAAWC,OAAO,CAACN;YACrB;QACF;IACF;AACF;AAEA,OAAO,SAASoE,MACdC,IAAY,EACZC,EAAU,EACVC,eAAuB,EACvBvH,EAA4C;IAE5C,MAAMwH,eAAetI,gBAAgBmI;IACrC,IAAIG,iBAAiB3G,WAAW;QAC9B,MAAM,qBAA2C,CAA3C,IAAIP,MAAM,4BAA4B+G,OAAtC,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA,2DAA2D;IAC3D,MAAMpH,eAAe,IAAId;IACzBmB,MAAMmH,iBAAiB,CAACxH,cAAcmH;IAEtC,MAAMM,OAAO1H,GAAG0H,IAAI;IACpB,MAAMC,WAAW;QACf,CAACD,KAAK,EAAE,eAAgB,GAAGxD,IAAW;YACpC,MAAMtE,YAAYvB,iBAAiBuJ,QAAQ;YAC3C,IAAIhI,cAAciB,WAAW;gBAC3B,MAAM,qBAEL,CAFK,IAAIP,MACR,4EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAMsB,gBAAgBnD,qBAAqBmJ,QAAQ;YAEnD,0EAA0E;YAC1E,sFAAsF;YACtF,MAAM9H,0BAA0BlB;YAEhC,qFAAqF;YACrF,wFAAwF;YACxF,qFAAqF;YACrF,sBAAsB;YACtB,MAAMiJ,UAAUjI,UAAUiI,OAAO;YAEjC,sEAAsE;YACtE,wEAAwE;YACxE,wEAAwE;YACxE,iEAAiE;YACjE,uEAAuE;YACvE,MAAMxG,iBAAiBO,iBAAiBtD,kBAAkBsD;YAE1D,MAAMkG,0BACJlG,CAAAA,iCAAAA,cAAehB,IAAI,MAAK,cACpBxB,8BAA8BwC,iBAC9Bf;YAEN,mEAAmE;YACnE,sEAAsE;YACtE,0EAA0E;YAC1E,sEAAsE;YACtE,oEAAoE;YACpE,wEAAwE;YACxE,yEAAyE;YACzE,SAAS;YACT,IAAI,CAACjB,UAAUmI,gBAAgB,IAAIC,gBAAgB9D,OAAO;gBACxD,MAAM,CAAC,EAAE+D,MAAM,EAAEC,YAAY,EAAE,CAAC,GAAGhE;gBACnC,iDAAiD;gBACjDA,OAAO;oBAAC;wBAAE+D;wBAAQC;oBAAa;iBAAE;gBAEjC,MAAMC,aAAanI;gBAEnBA,KAAK,CAAA;oBACH,CAAC0H,KAAK,EAAE,OAAO,EACbO,QAAQG,gBAAgB,EAC8B,GACtDD,WAAWnD,KAAK,CAAC,MAAM;4BACrB;gCACEiD,QAAQG;gCACRF,cACE7I,0CAA0CO;4BAC9C;yBACD;gBACL,CAAA,CAAC,CAAC8H,KAAK;YACT;YAEA,IAAIH,kBAAkB,GAAG;gBACvB,IAAIrD,KAAKjC,MAAM,KAAK,GAAG;oBACrB,MAAM,qBAEL,CAFK,IAAIlD,eACR,CAAC,kCAAkC,EAAEsJ,KAAKC,SAAS,CAACtI,GAAG0H,IAAI,EAAE,gEAAgE,CAAC,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMa,qBAAqBrE,KAAKsE,KAAK;gBACrC,MAAMC,YAAY,MAAM3J,uBAAuBwI,IAAIiB;gBAEnD,IAAI,CAACG,MAAMC,OAAO,CAACF,YAAY;oBAC7B,MAAM,qBAEL,CAFK,IAAI1J,eACR,CAAC,qDAAqD,EAAEsJ,KAAKC,SAAS,CAACtI,GAAG0H,IAAI,EAAE,mCAAmC,EAAE,OAAOe,UAAU,SAAS,CAAC,GAD5I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIlB,oBAAoBkB,UAAUxG,MAAM,EAAE;oBACxC,MAAM,qBAEL,CAFK,IAAIlD,eACR,CAAC,kCAAkC,EAAEsJ,KAAKC,SAAS,CAACtI,GAAG0H,IAAI,EAAE,YAAY,EAAEH,gBAAgB,sBAAsB,EAAEkB,UAAUxG,MAAM,CAAC,SAAS,CAAC,GAD1I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEAiC,KAAK0E,OAAO,CAACH;YACf;YAEA,MAAMxE,sBAAsB7F;YAC5B,MAAMyK,gBAA+B;gBAAChB;gBAASxG;gBAAgBiG;gBAAIpD;aAAK;YACxE,MAAM4E,uBAA0C,MAAM3K,YACpD0K,eACA;gBAAE5E;gBAAqBsB,QAAQuC;YAAwB;YAGzD,MAAMiB,qBACJ,OAAOD,yBAAyB,WAE5B,+CAA+C;YAC/CA,uBACA,MAAMvC,eAAeuC;YAE3B,IAAI1D,SAAqCvE;YAEzC,kEAAkE;YAClE,MAAMmI,2BAA2BpH,gBAC7BpD,4BAA4BoD,iBAC5B;YACJ,MAAMqH,wBAAwBrH,gBAC1BrD,yBAAyBqD,iBACzB;YAEJ,IAAIqH,uBAAuB;gBACzB,MAAMnF,cACJlC,iBAAiBA,cAAchB,IAAI,KAAK,cACpCgB,cAAckC,WAAW,GACzB;gBAEN,IAAIA,aAAa;oBACfA,YAAYoF,SAAS;gBACvB;gBACA,MAAMC,cAAcF,sBAAsB7B,KAAK,CAACgC,GAAG,CAACL;gBACpD,IAAII,gBAAgBtI,WAAW;oBAC7B,MAAMwI,gBAAgB,MAAMF;oBAC5BxH,0BAA0BC,eAAeyH;oBACzC,IACEzH,kBAAkBf,aAClBe,cAAchB,IAAI,KAAK,eACvByI,kBAAkBxI,aACjBwI,CAAAA,cAAc7I,UAAU,KAAK,KAC5B6I,cAAc5I,MAAM,GAAGxB,cAAa,GACtC;wBACA,6EAA6E;wBAC7E,4EAA4E;wBAC5E,gFAAgF;wBAChF,gFAAgF;wBAChF,IAAI6E,aAAa;4BACfA,YAAYC,OAAO;wBACrB;wBACA,OAAOpF,mBACLiD,cAAc2C,YAAY,EAC1B;oBAEJ;oBACA,MAAM,CAACyB,SAASC,QAAQ,GAAGoD,cAAcrG,KAAK,CAAC6C,GAAG;oBAClDwD,cAAcrG,KAAK,GAAGiD;oBAEtB,IAAInC,aAAa;wBACf,mEAAmE;wBACnE,gCAAgC;wBAChCsB,SAAS+B,4BAA4BnB,SAASlC;oBAChD,OAAO;wBACLsB,SAASY;oBACX;gBACF,OAAO;oBACL,IAAIlC,aAAa;wBACfA,YAAYC,OAAO;oBACrB;gBACF;YACF;YAEA,IAAIqB,WAAWvE,WAAW;gBACxB,MAAMiD,cACJlC,iBAAiBA,cAAchB,IAAI,KAAK,cACpCgB,cAAckC,WAAW,GACzB;gBACN,IAAIA,aAAa;oBACf,6EAA6E;oBAC7E,2DAA2D;oBAC3DA,YAAYoF,SAAS;gBACvB;gBAEA,MAAMlI,eACJY,kBAAkBf,aAAae,cAAchB,IAAI,KAAK,mBAClD,EAAE,GACFgB,cAAcZ,YAAY;gBAEhC,MAAMQ,kBAAkBC,sBAAsB7B,WAAWgC;gBAEzD,MAAMC,QAAQL,kBACVX,YACA,MAAM2G,aAAa4B,GAAG,CAACL,oBAAoB/H;gBAE/C,MAAMsI,cAAc3E,YAAYC,UAAU,GAAGD,YAAYE,GAAG;gBAC5D,IACEjD,kBAAkBf,aAClBe,cAAchB,IAAI,KAAK,eACvBiB,UAAUhB,aACTgB,CAAAA,MAAMrB,UAAU,KAAK,KAAKqB,MAAMpB,MAAM,GAAGxB,cAAa,GACvD;oBACA,6EAA6E;oBAC7E,4EAA4E;oBAC5E,gFAAgF;oBAChF,gFAAgF;oBAChF,IAAI6E,aAAa;wBACfA,YAAYC,OAAO;oBACrB;oBAEA,OAAOpF,mBACLiD,cAAc2C,YAAY,EAC1B;gBAEJ,OAAO,IACL1C,UAAUhB,aACVyI,cAAczH,MAAMgC,SAAS,GAAGhC,MAAMpB,MAAM,GAAG,QAC9Cb,UAAU2J,kBAAkB,IAC3BD,cAAczH,MAAMgC,SAAS,GAAGhC,MAAMrB,UAAU,GAAG,MACrD;oBACA,+BAA+B;oBAE/B,+EAA+E;oBAC/E,+EAA+E;oBAC/E,4EAA4E;oBAE5E,kFAAkF;oBAClF,mFAAmF;oBACnF,+EAA+E;oBAC/E,mFAAmF;oBACnF,6EAA6E;oBAE7E,MAAM,CAACgJ,WAAWpD,kBAAkB,GAAG,MAAMzG,mBAC3CC,WACAgC,eACA9B,yBACAgJ,sBACA9I,IACAC;oBAGF,IAAIwJ;oBACJ,IAAIT,0BAA0B;wBAC5B,8DAA8D;wBAC9D,MAAM1C,QAAQH,uBAAuBC;wBACrCqD,kBAAkBpD,iBAAiBC,OAAO;wBAC1C0C,yBAAyB5B,KAAK,CAACsC,GAAG,CAChCX,oBACA1C,iBAAiBC,OAAO;oBAE5B,OAAO;wBACLmD,kBAAkBrD;oBACpB;oBAEA,MAAMuD,UAAUnC,aAAakC,GAAG,CAACX,oBAAoBU;oBAErD,IAAI,CAAC7J,UAAUgK,uBAAuB,EAAE;wBACtChK,UAAUgK,uBAAuB,GAAG,EAAE;oBACxC;oBACAhK,UAAUgK,uBAAuB,CAACxH,IAAI,CAACuH;oBAEvCvE,SAASoE;gBACX,OAAO;oBACL7H,0BAA0BC,eAAeC;oBAEzC,qDAAqD;oBACrDuD,SAASvD,MAAMmB,KAAK;oBAEpB,qEAAqE;oBACrE,yBAAyB;oBACzB,IAAIgG,0BAA0B;wBAC5B,MAAM,CAACa,WAAWC,WAAW,GAAG/D,gBAAgBlE;wBAChD,IAAIiC,aAAa;4BACfsB,SAAS+B,4BAA4B0C,UAAU7G,KAAK,EAAEc;wBACxD,OAAO;4BACLsB,SAASyE,UAAU7G,KAAK;wBAC1B;wBAEAgG,yBAAyB5B,KAAK,CAACsC,GAAG,CAChCX,oBACA1E,QAAQC,OAAO,CAACwF;oBAEpB,OAAO;wBACL,kEAAkE;wBAClE,wEAAwE;wBACxE,kCAAkC;wBAClChG,+BAAAA,YAAaC,OAAO;oBACtB;oBAEA,IAAIuF,cAAczH,MAAMgC,SAAS,GAAGhC,MAAMrB,UAAU,GAAG,MAAM;wBAC3D,gFAAgF;wBAChF,mEAAmE;wBACnE,MAAM,CAACuJ,eAAe3D,kBAAkB,GAAG,MAAMzG,mBAC/CC,WACAiB,WACAf,yBACAgJ,sBACA9I,IACAC;wBAGF,IAAIwJ;wBACJ,IAAIT,0BAA0B;4BAC5B,MAAM1C,QAAQH,uBAAuBC;4BACrCqD,kBAAkBpD,iBAAiBC,OAAO;4BAC1C0C,yBAAyB5B,KAAK,CAACsC,GAAG,CAChCX,oBACA1C,iBAAiBC,OAAO;wBAE5B,OAAO;4BACLmD,kBAAkBrD;wBACpB;wBAEA,MAAMuD,UAAUnC,aAAakC,GAAG,CAC9BX,oBACAU;wBAGF,IAAI,CAAC7J,UAAUgK,uBAAuB,EAAE;4BACtChK,UAAUgK,uBAAuB,GAAG,EAAE;wBACxC;wBACAhK,UAAUgK,uBAAuB,CAACxH,IAAI,CAACuH;wBAEvC,MAAMI,cAAcC,MAAM;oBAC5B;gBACF;YACF;YAEA,yFAAyF;YACzF,0FAA0F;YAC1F,wFAAwF;YACxF,uFAAuF;YACvF,qFAAqF;YACrF,uFAAuF;YACvF,iFAAiF;YACjF,MAAMC,oBAAoB;YAE1B,MAAMC,yBAAyB;gBAC7B,2FAA2F;gBAC3F,yFAAyF;gBACzF,+CAA+C;gBAC/CC,eAAe;gBACfC,WAAW7K,gBACPO,wBAAwBuK,oBAAoB,GAC5CvK,wBAAwBwK,gBAAgB;gBAC5CC,iBAAiB1L;YACnB;YAEA,OAAOX,yBAAyBkH,QAAQ;gBACtC8E;gBACAjG;gBACAgG;gBACA3E,iBAAiB;YACnB;QACF;IACF,CAAC,CAACoC,KAAK;IAEP,OAAOpI,MAAM8H,KAAK,CAACO;AACrB;AAEA;;CAEC,GACD,SAAS5C,iBACP/E,EAA0B;IAE1B,IAAIwK;IAEJ,OAAO;QACLC,MAAKC,WAAW,EAAEC,UAAU;YAC1B,IAAI,CAACH,eAAe;gBAClBA,gBAAgBxK;YAClB;YAEA,OAAOwK,cAAcC,IAAI,CAACC,aAAaC;QACzC;IACF;AACF;AAEA,SAAS3C,gBACP9D,IAAW;IAEX,IAAIA,KAAKjC,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,CAAC2I,OAAOC,IAAI,GAAG3G;IAErB,OACE2G,QAAQhK,aAAa,iDAAiD;IACtE+J,UAAU,QACV,OAAOA,UAAU,YACjB,AAACA,MAAqCE,iBAAiB;AAE3D;AAEA,SAASrJ,sBACP7B,SAAoB,EACpBgC,aAAwC;IAExC,IAAIhC,UAAUmL,oBAAoB,EAAE;QAClC,OAAO;IACT;IAEA,IAAInL,UAAUoL,GAAG,IAAIpJ,eAAe;QAClC,IAAIA,cAAchB,IAAI,KAAK,WAAW;YACpC,OAAOgB,cAAcqJ,OAAO,CAAC7B,GAAG,CAAC,qBAAqB;QACxD;QAEA,IAAIxH,cAAchB,IAAI,KAAK,SAAS;YAClC,OAAOgB,cAAcJ,eAAe;QACtC;IACF;IAEA,OAAO;AACT"}