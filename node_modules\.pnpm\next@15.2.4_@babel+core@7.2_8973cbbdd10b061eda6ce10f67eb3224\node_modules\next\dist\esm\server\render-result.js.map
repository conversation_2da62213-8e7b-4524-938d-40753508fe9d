{"version": 3, "sources": ["../../src/server/render-result.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders, ServerResponse } from 'http'\nimport type { CacheControl } from './lib/cache-control'\nimport type { FetchMetrics } from './base-http'\n\nimport {\n  chainStreams,\n  streamFromBuffer,\n  streamFromString,\n  streamToBuffer,\n  streamToString,\n} from './stream-utils/node-web-streams-helper'\nimport { isAbortError, pipeToNodeResponse } from './pipe-readable'\nimport type { RenderResumeDataCache } from './resume-data-cache/resume-data-cache'\n\ntype ContentTypeOption = string | undefined\n\nexport type AppPageRenderResultMetadata = {\n  flightData?: Buffer\n  cacheControl?: CacheControl\n  staticBailoutInfo?: {\n    stack?: string\n    description?: string\n  }\n\n  /**\n   * The postponed state if the render had postponed and needs to be resumed.\n   */\n  postponed?: string\n\n  /**\n   * The headers to set on the response that were added by the render.\n   */\n  headers?: OutgoingHttpHeaders\n  fetchTags?: string\n  fetchMetrics?: FetchMetrics\n\n  segmentData?: Map<string, Buffer>\n\n  /**\n   * In development, the cache is warmed up before the render. This is attached\n   * to the metadata so that it can be used during the render.\n   */\n  devRenderResumeDataCache?: RenderResumeDataCache\n}\n\nexport type PagesRenderResultMetadata = {\n  pageData?: any\n  cacheControl?: CacheControl\n  assetQueryString?: string\n  isNotFound?: boolean\n  isRedirect?: boolean\n}\n\nexport type StaticRenderResultMetadata = {}\n\nexport type RenderResultMetadata = AppPageRenderResultMetadata &\n  PagesRenderResultMetadata &\n  StaticRenderResultMetadata\n\nexport type RenderResultResponse =\n  | ReadableStream<Uint8Array>[]\n  | ReadableStream<Uint8Array>\n  | string\n  | Buffer\n  | null\n\nexport type RenderResultOptions<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> = {\n  contentType?: ContentTypeOption\n  waitUntil?: Promise<unknown>\n  metadata: Metadata\n}\n\nexport default class RenderResult<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> {\n  /**\n   * The detected content type for the response. This is used to set the\n   * `Content-Type` header.\n   */\n  public readonly contentType: ContentTypeOption\n\n  /**\n   * The metadata for the response. This is used to set the revalidation times\n   * and other metadata.\n   */\n  public readonly metadata: Readonly<Metadata>\n\n  /**\n   * The response itself. This can be a string, a stream, or null. If it's a\n   * string, then it's a static response. If it's a stream, then it's a\n   * dynamic response. If it's null, then the response was not found or was\n   * already sent.\n   */\n  private response: RenderResultResponse\n\n  /**\n   * Creates a new RenderResult instance from a static response.\n   *\n   * @param value the static response value\n   * @returns a new RenderResult instance\n   */\n  public static fromStatic(value: string | Buffer) {\n    return new RenderResult<StaticRenderResultMetadata>(value, { metadata: {} })\n  }\n\n  private readonly waitUntil?: Promise<unknown>\n\n  constructor(\n    response: RenderResultResponse,\n    { contentType, waitUntil, metadata }: RenderResultOptions<Metadata>\n  ) {\n    this.response = response\n    this.contentType = contentType\n    this.metadata = metadata\n    this.waitUntil = waitUntil\n  }\n\n  public assignMetadata(metadata: Metadata) {\n    Object.assign(this.metadata, metadata)\n  }\n\n  /**\n   * Returns true if the response is null. It can be null if the response was\n   * not found or was already sent.\n   */\n  public get isNull(): boolean {\n    return this.response === null\n  }\n\n  /**\n   * Returns false if the response is a string. It can be a string if the page\n   * was prerendered. If it's not, then it was generated dynamically.\n   */\n  public get isDynamic(): boolean {\n    return typeof this.response !== 'string'\n  }\n\n  public toUnchunkedBuffer(stream?: false): Buffer\n  public toUnchunkedBuffer(stream: true): Promise<Buffer>\n  public toUnchunkedBuffer(stream = false): Promise<Buffer> | Buffer {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToBuffer(this.readable)\n    }\n\n    return Buffer.from(this.response)\n  }\n\n  /**\n   * Returns the response if it is a string. If the page was dynamic, this will\n   * return a promise if the `stream` option is true, or it will throw an error.\n   *\n   * @param stream Whether or not to return a promise if the response is dynamic\n   * @returns The response as a string\n   */\n  public toUnchunkedString(stream?: false): string\n  public toUnchunkedString(stream: true): Promise<string>\n  public toUnchunkedString(stream = false): Promise<string> | string {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToString(this.readable)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Returns the response if it is a stream, or throws an error if it is a\n   * string.\n   */\n  private get readable(): ReadableStream<Uint8Array> {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be streamed')\n    }\n    if (typeof this.response === 'string') {\n      throw new Error('Invariant: static responses cannot be streamed')\n    }\n\n    if (Buffer.isBuffer(this.response)) {\n      return streamFromBuffer(this.response)\n    }\n\n    // If the response is an array of streams, then chain them together.\n    if (Array.isArray(this.response)) {\n      return chainStreams(...this.response)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Chains a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the end. When this response is piped, all of the streams will be piped\n   * one after the other.\n   *\n   * @param readable The new stream to chain\n   */\n  public chain(readable: ReadableStream<Uint8Array>) {\n    if (this.response === null) {\n      throw new Error('Invariant: response is null. This is a bug in Next.js')\n    }\n\n    // If the response is not an array of streams already, make it one.\n    let responses: ReadableStream<Uint8Array>[]\n    if (typeof this.response === 'string') {\n      responses = [streamFromString(this.response)]\n    } else if (Array.isArray(this.response)) {\n      responses = this.response\n    } else if (Buffer.isBuffer(this.response)) {\n      responses = [streamFromBuffer(this.response)]\n    } else {\n      responses = [this.response]\n    }\n\n    // Add the new stream to the array.\n    responses.push(readable)\n\n    // Update the response.\n    this.response = responses\n  }\n\n  /**\n   * Pipes the response to a writable stream. This will close/cancel the\n   * writable stream if an error is encountered. If this doesn't throw, then\n   * the writable stream will be closed or aborted.\n   *\n   * @param writable Writable stream to pipe the response to\n   */\n  public async pipeTo(writable: WritableStream<Uint8Array>): Promise<void> {\n    try {\n      await this.readable.pipeTo(writable, {\n        // We want to close the writable stream ourselves so that we can wait\n        // for the waitUntil promise to resolve before closing it. If an error\n        // is encountered, we'll abort the writable stream if we swallowed the\n        // error.\n        preventClose: true,\n      })\n\n      // If there is a waitUntil promise, wait for it to resolve before\n      // closing the writable stream.\n      if (this.waitUntil) await this.waitUntil\n\n      // Close the writable stream.\n      await writable.close()\n    } catch (err) {\n      // If this is an abort error, we should abort the writable stream (as we\n      // took ownership of it when we started piping). We don't need to re-throw\n      // because we handled the error.\n      if (isAbortError(err)) {\n        // Abort the writable stream if an error is encountered.\n        await writable.abort(err)\n\n        return\n      }\n\n      // We're not aborting the writer here as when this method throws it's not\n      // clear as to how so the caller should assume it's their responsibility\n      // to clean up the writer.\n      throw err\n    }\n  }\n\n  /**\n   * Pipes the response to a node response. This will close/cancel the node\n   * response if an error is encountered.\n   *\n   * @param res\n   */\n  public async pipeToNodeResponse(res: ServerResponse) {\n    await pipeToNodeResponse(this.readable, res, this.waitUntil)\n  }\n}\n"], "names": ["chainStreams", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "isAbortError", "pipeToNodeResponse", "RenderResult", "fromStatic", "value", "metadata", "constructor", "response", "contentType", "waitUntil", "assignMetadata", "Object", "assign", "isNull", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stream", "Error", "readable", "<PERSON><PERSON><PERSON>", "from", "toUnchunkedString", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "chain", "responses", "push", "pipeTo", "writable", "preventClose", "close", "err", "abort", "res"], "mappings": "AAIA,SACEA,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,QACT,yCAAwC;AAC/C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;AA+DlE,eAAe,MAAMC;IAuBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAsB,EAAE;QAC/C,OAAO,IAAIF,aAAyCE,OAAO;YAAEC,UAAU,CAAC;QAAE;IAC5E;IAIAC,YACEC,QAA8B,EAC9B,EAAEC,WAAW,EAAEC,SAAS,EAAEJ,QAAQ,EAAiC,CACnE;QACA,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACI,SAAS,GAAGA;IACnB;IAEOC,eAAeL,QAAkB,EAAE;QACxCM,OAAOC,MAAM,CAAC,IAAI,CAACP,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWQ,SAAkB;QAC3B,OAAO,IAAI,CAACN,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWO,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACP,QAAQ,KAAK;IAClC;IAIOQ,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,qBAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,qBAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOnB,eAAe,IAAI,CAACoB,QAAQ;QACrC;QAEA,OAAOC,OAAOC,IAAI,CAAC,IAAI,CAACb,QAAQ;IAClC;IAWOc,kBAAkBL,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,qBAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,qBAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOlB,eAAe,IAAI,CAACmB,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACX,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYW,WAAuC;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,qBAAyD,CAAzD,IAAIU,MAAM,iDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwD;QAChE;QACA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,MAAM,qBAA2D,CAA3D,IAAIU,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,IAAIE,OAAOG,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YAClC,OAAOX,iBAAiB,IAAI,CAACW,QAAQ;QACvC;QAEA,oEAAoE;QACpE,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YAChC,OAAOZ,gBAAgB,IAAI,CAACY,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACD,AAAOkB,MAAMP,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,qBAAkE,CAAlE,IAAIU,MAAM,0DAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiE;QACzE;QAEA,mEAAmE;QACnE,IAAIS;QACJ,IAAI,OAAO,IAAI,CAACnB,QAAQ,KAAK,UAAU;YACrCmB,YAAY;gBAAC7B,iBAAiB,IAAI,CAACU,QAAQ;aAAE;QAC/C,OAAO,IAAIgB,MAAMC,OAAO,CAAC,IAAI,CAACjB,QAAQ,GAAG;YACvCmB,YAAY,IAAI,CAACnB,QAAQ;QAC3B,OAAO,IAAIY,OAAOG,QAAQ,CAAC,IAAI,CAACf,QAAQ,GAAG;YACzCmB,YAAY;gBAAC9B,iBAAiB,IAAI,CAACW,QAAQ;aAAE;QAC/C,OAAO;YACLmB,YAAY;gBAAC,IAAI,CAACnB,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCmB,UAAUC,IAAI,CAACT;QAEf,uBAAuB;QACvB,IAAI,CAACX,QAAQ,GAAGmB;IAClB;IAEA;;;;;;GAMC,GACD,MAAaE,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACX,QAAQ,CAACU,MAAM,CAACC,UAAU;gBACnC,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,SAAS;gBACTC,cAAc;YAChB;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,IAAI,IAAI,CAACrB,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS;YAExC,6BAA6B;YAC7B,MAAMoB,SAASE,KAAK;QACtB,EAAE,OAAOC,KAAK;YACZ,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC,IAAIhC,aAAagC,MAAM;gBACrB,wDAAwD;gBACxD,MAAMH,SAASI,KAAK,CAACD;gBAErB;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,MAAMA;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAa/B,mBAAmBiC,GAAmB,EAAE;QACnD,MAAMjC,mBAAmB,IAAI,CAACiB,QAAQ,EAAEgB,KAAK,IAAI,CAACzB,SAAS;IAC7D;AACF"}