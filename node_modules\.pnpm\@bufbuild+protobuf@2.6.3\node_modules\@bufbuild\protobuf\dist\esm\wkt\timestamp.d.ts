import type { Timestamp } from "./gen/google/protobuf/timestamp_pb.js";
/**
 * Create a google.protobuf.Timestamp for the current time.
 */
export declare function timestampNow(): Timestamp;
/**
 * Create a google.protobuf.Timestamp message from an ECMAScript Date.
 */
export declare function timestampFromDate(date: Date): Timestamp;
/**
 * Convert a google.protobuf.Timestamp message to an ECMAScript Date.
 */
export declare function timestampDate(timestamp: Timestamp): Date;
/**
 * Create a google.protobuf.Timestamp message from a Unix timestamp in milliseconds.
 */
export declare function timestampFromMs(timestampMs: number): Timestamp;
/**
 * Convert a google.protobuf.Timestamp to a Unix timestamp in milliseconds.
 */
export declare function timestampMs(timestamp: Timestamp): number;
