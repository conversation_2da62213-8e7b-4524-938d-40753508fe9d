import type { GenFile, GenMessage } from "../../../../codegenv2/types.js";
import type { Message } from "../../../../types.js";
/**
 * Describes the file google/protobuf/wrappers.proto.
 */
export declare const file_google_protobuf_wrappers: GenFile;
/**
 * Wrapper message for `double`.
 *
 * The JSON representation for `DoubleValue` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.DoubleValue
 */
export type DoubleValue = Message<"google.protobuf.DoubleValue"> & {
    /**
     * The double value.
     *
     * @generated from field: double value = 1;
     */
    value: number;
};
/**
 * Wrapper message for `double`.
 *
 * The JSON representation for `DoubleValue` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.DoubleValue
 */
export type DoubleValueJson = number | "NaN" | "Infinity" | "-Infinity";
/**
 * Describes the message google.protobuf.DoubleValue.
 * Use `create(DoubleValueSchema)` to create a new message.
 */
export declare const DoubleValueSchema: GenMessage<DoubleValue, {
    jsonType: DoubleValueJson;
}>;
/**
 * Wrapper message for `float`.
 *
 * The JSON representation for `FloatValue` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.FloatValue
 */
export type FloatValue = Message<"google.protobuf.FloatValue"> & {
    /**
     * The float value.
     *
     * @generated from field: float value = 1;
     */
    value: number;
};
/**
 * Wrapper message for `float`.
 *
 * The JSON representation for `FloatValue` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.FloatValue
 */
export type FloatValueJson = number | "NaN" | "Infinity" | "-Infinity";
/**
 * Describes the message google.protobuf.FloatValue.
 * Use `create(FloatValueSchema)` to create a new message.
 */
export declare const FloatValueSchema: GenMessage<FloatValue, {
    jsonType: FloatValueJson;
}>;
/**
 * Wrapper message for `int64`.
 *
 * The JSON representation for `Int64Value` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.Int64Value
 */
export type Int64Value = Message<"google.protobuf.Int64Value"> & {
    /**
     * The int64 value.
     *
     * @generated from field: int64 value = 1;
     */
    value: bigint;
};
/**
 * Wrapper message for `int64`.
 *
 * The JSON representation for `Int64Value` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.Int64Value
 */
export type Int64ValueJson = string;
/**
 * Describes the message google.protobuf.Int64Value.
 * Use `create(Int64ValueSchema)` to create a new message.
 */
export declare const Int64ValueSchema: GenMessage<Int64Value, {
    jsonType: Int64ValueJson;
}>;
/**
 * Wrapper message for `uint64`.
 *
 * The JSON representation for `UInt64Value` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.UInt64Value
 */
export type UInt64Value = Message<"google.protobuf.UInt64Value"> & {
    /**
     * The uint64 value.
     *
     * @generated from field: uint64 value = 1;
     */
    value: bigint;
};
/**
 * Wrapper message for `uint64`.
 *
 * The JSON representation for `UInt64Value` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.UInt64Value
 */
export type UInt64ValueJson = string;
/**
 * Describes the message google.protobuf.UInt64Value.
 * Use `create(UInt64ValueSchema)` to create a new message.
 */
export declare const UInt64ValueSchema: GenMessage<UInt64Value, {
    jsonType: UInt64ValueJson;
}>;
/**
 * Wrapper message for `int32`.
 *
 * The JSON representation for `Int32Value` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.Int32Value
 */
export type Int32Value = Message<"google.protobuf.Int32Value"> & {
    /**
     * The int32 value.
     *
     * @generated from field: int32 value = 1;
     */
    value: number;
};
/**
 * Wrapper message for `int32`.
 *
 * The JSON representation for `Int32Value` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.Int32Value
 */
export type Int32ValueJson = number;
/**
 * Describes the message google.protobuf.Int32Value.
 * Use `create(Int32ValueSchema)` to create a new message.
 */
export declare const Int32ValueSchema: GenMessage<Int32Value, {
    jsonType: Int32ValueJson;
}>;
/**
 * Wrapper message for `uint32`.
 *
 * The JSON representation for `UInt32Value` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.UInt32Value
 */
export type UInt32Value = Message<"google.protobuf.UInt32Value"> & {
    /**
     * The uint32 value.
     *
     * @generated from field: uint32 value = 1;
     */
    value: number;
};
/**
 * Wrapper message for `uint32`.
 *
 * The JSON representation for `UInt32Value` is JSON number.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.UInt32Value
 */
export type UInt32ValueJson = number;
/**
 * Describes the message google.protobuf.UInt32Value.
 * Use `create(UInt32ValueSchema)` to create a new message.
 */
export declare const UInt32ValueSchema: GenMessage<UInt32Value, {
    jsonType: UInt32ValueJson;
}>;
/**
 * Wrapper message for `bool`.
 *
 * The JSON representation for `BoolValue` is JSON `true` and `false`.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.BoolValue
 */
export type BoolValue = Message<"google.protobuf.BoolValue"> & {
    /**
     * The bool value.
     *
     * @generated from field: bool value = 1;
     */
    value: boolean;
};
/**
 * Wrapper message for `bool`.
 *
 * The JSON representation for `BoolValue` is JSON `true` and `false`.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.BoolValue
 */
export type BoolValueJson = boolean;
/**
 * Describes the message google.protobuf.BoolValue.
 * Use `create(BoolValueSchema)` to create a new message.
 */
export declare const BoolValueSchema: GenMessage<BoolValue, {
    jsonType: BoolValueJson;
}>;
/**
 * Wrapper message for `string`.
 *
 * The JSON representation for `StringValue` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.StringValue
 */
export type StringValue = Message<"google.protobuf.StringValue"> & {
    /**
     * The string value.
     *
     * @generated from field: string value = 1;
     */
    value: string;
};
/**
 * Wrapper message for `string`.
 *
 * The JSON representation for `StringValue` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.StringValue
 */
export type StringValueJson = string;
/**
 * Describes the message google.protobuf.StringValue.
 * Use `create(StringValueSchema)` to create a new message.
 */
export declare const StringValueSchema: GenMessage<StringValue, {
    jsonType: StringValueJson;
}>;
/**
 * Wrapper message for `bytes`.
 *
 * The JSON representation for `BytesValue` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.BytesValue
 */
export type BytesValue = Message<"google.protobuf.BytesValue"> & {
    /**
     * The bytes value.
     *
     * @generated from field: bytes value = 1;
     */
    value: Uint8Array;
};
/**
 * Wrapper message for `bytes`.
 *
 * The JSON representation for `BytesValue` is JSON string.
 *
 * Not recommended for use in new APIs, but still useful for legacy APIs and
 * has no plan to be removed.
 *
 * @generated from message google.protobuf.BytesValue
 */
export type BytesValueJson = string;
/**
 * Describes the message google.protobuf.BytesValue.
 * Use `create(BytesValueSchema)` to create a new message.
 */
export declare const BytesValueSchema: GenMessage<BytesValue, {
    jsonType: BytesValueJson;
}>;
